import { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorState } from '@shared/components';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * ErrorBoundary bileşeni, alt bileşenlerde oluşan hataları yakalar ve
 * kullanıcıya dost bir hata mesajı gösterir.
 * 
 * Kullanım:
 * <ErrorBoundary>
 *   <YourComponent />
 * </ErrorBoundary>
 */
class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // Bir sonraki render'da hata UI'ını göstermek için state'i güncelle
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Özel hata işleme callback'i varsa çağır
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  public render() {
    if (this.state.hasError) {
      // Özel fallback bileşeni varsa onu göster
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Varsayılan hata durumu bileşenini göster
      return (
        <ErrorState 
          message={`Bir hata oluştu: ${this.state.error?.message || 'Bilinmeyen hata'}`}
          onRetry={() => {
            this.setState({ hasError: false, error: null });
            window.location.reload();
          }}
        />
      );
    }

    // Hata yoksa normal içeriği göster
    return this.props.children;
  }
}

export default ErrorBoundary;
