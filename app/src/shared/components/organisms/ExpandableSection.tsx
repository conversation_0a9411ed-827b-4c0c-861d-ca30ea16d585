import { useState, useMemo, memo, FC } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { IBook, ICategory } from '@domains/library/models/types';
import { BookCard } from '@shared/components';

interface IExpandableSectionProps {
  /** Başlık metni */
  title: string;
  /** Gösterilecek kitap öğeleri dizisi */
  items: IBook[];
  /** Kategori nesnesi */
  category: ICategory;
  /** Açık/kapalı başlangıç durumu (varsayılan: true) */
  initialExpanded?: boolean;
  /** Özel CSS sınıfı */
  className?: string;
  /** Öğeler için alternatif render fonksiyonu */
  renderItem?: (item: IBook, category: ICategory) => React.ReactNode;
  /** Özel içerik */
  customContent?: React.ReactNode;
}

const ExpandableSection: FC<IExpandableSectionProps> = ({
  title,
  items,
  category,
  initialExpanded = true,
  className = '',
  renderItem,
  customContent
}: IExpandableSectionProps) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const categorySlug = title.toLowerCase().replace(/\s+/g, '-');
  const headerBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const contentBgColor = useAutoOverlay(3, 'var(--bg-color)');

  // Filter available items
  const availableItems = useMemo(() =>
    items.filter(item => item.is_available),
    [items]
  );

  // If no available items and no custom content, don't render the section
  if (availableItems.length === 0 && !customContent) {
    return null;
  }

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={`section category-${categorySlug} flex flex-col ${className} mx-auto max-w-6xl w-full`}
    >
      <div
        className={`section-header ${isExpanded ? 'expanded' : ''} p-3 cursor-pointer flex items-center gap-3 transition-all duration-200`}
        style={{
          backgroundColor: headerBgColor,
          borderRadius: isExpanded ? '10px 10px 0 0' : '10px'
        }}
        onClick={toggleExpand}
        aria-expanded={isExpanded}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleExpand();
          }
        }}
      >
        <svg
          viewBox="0 0 24 24"
          width="24"
          height="24"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={`text-[var(--text-color)] transition-transform duration-200 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}
          aria-hidden="true"
        >
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
        <h2 className="font-bold text-[1.5rem] m-0 text-[var(--text-color)]">{title}</h2>
      </div>
      {isExpanded && (
        <div
          className="section-content p-4 rounded-b-lg"
          style={{
            backgroundColor: contentBgColor,
          }}
        >
          {/* Custom content varsa onu göster */}
          {customContent ? (
            customContent
          ) : (
            /* Normal kitap grid'i */
            <div className={`
              grid gap-4
              grid-cols-2
              sm:grid-cols-3
              md:grid-cols-4
              lg:grid-cols-[repeat(auto-fill,minmax(200px,1fr))]
            `}>
              {availableItems.map(item => (
                <div key={item.id} className="flex h-full">
                  {renderItem ?
                    renderItem(item, category) :
                    <BookCard book={item} category={category} />
                  }
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default memo(ExpandableSection);
export { ExpandableSection };