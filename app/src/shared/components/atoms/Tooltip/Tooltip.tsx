import React from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay'; // Corrected casing

interface TooltipProps {
  content: React.ReactNode;
  position: { top: number; left: number } | null;
  isVisible: boolean;
  onClose?: () => void; // Optional close handler
}

const Tooltip: React.FC<TooltipProps> = ({ content, position, isVisible, onClose }) => {
  // Use the autoOverlay hook to get a dynamic background color
  // Changed overlay percentage to 21%
  const dynamicBgColor = useAutoOverlay(21, 'var(--bg-color)');

  if (!isVisible || !position) {
    return null;
  }

  // Basic style for the main tooltip box
  const tooltipStyle: React.CSSProperties = {
    position: 'absolute',
    top: `${position.top}px`,
    left: `${position.left}px`,
    transform: 'translate(-50%, -110%)', // Position above and centered
    zIndex: 1000, // Ensure it's above other content
    // Use the dynamic color from the hook
    backgroundColor: dynamicBgColor || 'var(--bg-color)', // Fallback just in case
    color: 'var(--tooltip-text-color, var(--text-color))',
    padding: '0.6rem 0.9rem',
    borderRadius: '6px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
    maxWidth: '300px',
    fontSize: '0.9rem',
    lineHeight: '1.5',
  };

  // Style for the arrow element
  const arrowStyle: React.CSSProperties = {
    position: 'absolute',
    bottom: '-11px', // Adjusted for 11px size
    left: '50%',
    transform: 'translateX(-50%)',
    width: '0',
    height: '0',
    borderLeft: '11px solid transparent', // Set size back to 11px
    borderRight: '11px solid transparent', // Set size back to 11px
    // Use the same dynamic background color (21% overlay)
    borderTop: `11px solid ${dynamicBgColor || 'var(--bg-color)'}`, // Set size back to 11px
  };

  // Style for the overlay to close the tooltip
  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 999, // Below tooltip, above content
  };

  return (
    <>
      {/* Overlay for closing */} 
      {onClose && <div style={overlayStyle} onClick={onClose} />} 
      
      {/* Tooltip Container (Relative for Arrow Positioning) */}
      {/* Note: The outer div seems unnecessary now, applying positioning directly */} 
      <div style={tooltipStyle}> 
        {content}
        {/* Arrow Element */}
        <div style={arrowStyle}></div>
      </div>
    </>
  );
};

export default Tooltip; 