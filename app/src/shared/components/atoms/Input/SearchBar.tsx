import React from 'react';
import { memo } from 'react';
import { Search } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const SearchBar = ({ 
  placeholder = 'Search...', 
  value, 
  onChange, 
  className = '' 
}: SearchBarProps) => {
  const bgColor = useAutoOverlay(5, 'var(--bg-color)');
  const placeholderColor = useAutoOverlay(10, 'var(--text-color)');
  
  return (
    <div 
      className={`relative rounded-lg overflow-hidden ${className}`}
    >
      <div
        className="absolute left-3 top-1/2 transform -translate-y-1/2 opacity-60 text-[var(--text-color)]"
      >
        <Search size={16} />
      </div>
      <input
        type="text"
        className="w-full py-2 px-4 pl-9 text-sm text-[var(--text-color)] border-none outline-none placeholder:text-[var(--placeholder-color)]"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        style={{
          backgroundColor: bgColor,
          '--placeholder-color': placeholderColor
        } as React.CSSProperties}
      />
    </div>
  );
};

export default memo(SearchBar);
export { SearchBar }; 