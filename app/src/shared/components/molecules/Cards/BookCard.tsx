import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { IBook, ICategory } from '@domains/library/models/types';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface IBookCardProps {
  /** Gösterilecek kitap */
  book: IBook;
  /** Kitabın ait olduğu kategori */
  category: ICategory;
  /** Card'a tıklandığında tetiklenecek fonksiyon */
  onClick?: (book: IBook) => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Kitap detay sayfasına gitmek yerine özel işleme */
  disableNavigation?: boolean;
}

const BookCard = ({ book, category, onClick, className, disableNavigation }: IBookCardProps) => {
  const mixedBgColor = useAutoOverlay(13, 'var(--bg-color)');
  
  // Memoize the gradient style calculation
  const gradientStyle = useMemo(() => ({
    background: `linear-gradient(145deg, 
      ${category.gradient_start || '#4A5568'} 0%,
      ${category.gradient_start || '#4A5568'} 40%,
      ${category.gradient_end || '#2D3748'} 99%,
      ${category.gradient_end || '#2D3748'} 100%
    )`,
    color: '#FFFFFF',
    border: 'none',
    padding: '16px',
    height: '41px',
    borderRadius: '10px 10px 0 0',
    cursor: 'pointer'
  }), [category.gradient_start, category.gradient_end]);

  // Determine navigation path based on book category
  const navigationPath = useMemo(() => {
    if (category.id === 1) { // Kur'an kategorisi
      // Kitap ID'sine göre farklı sure sayfalarına yönlendir (Ancak ID'yi URL'e ekleme!)
      switch (book.id) {
        case 1:
          return `/kuran-sureler`;  // Sadece base path
        case 2:
          return `/mealli-kuran-sureler`; // Sadece base path
        case 3:
          return `/kelime-mealli-kuran-sureler`; // Sadece base path
        case 4:
          return `/kuran-meali-sureler`; // Sadece base path
        default:
          return `/kuran-sureler`; // Varsayılan
      }
    } else if (category.id === 2 || category.id === 3) {
      return `/risale/${book.id}`; // Risale için ID gerekli
    }
    return `/book/${book.id}`; // Diğer kitaplar için ID gerekli olabilir
  }, [category.id, book.id]);

  return (
    <div 
      className={`book-card-wrapper w-full max-w-[400px] flex flex-col ${className || ''}`}
    >
      {disableNavigation ? (
        <div
          className="book-card w-full p-0 m-0 rounded-lg overflow-hidden cursor-pointer bg-transparent flex flex-col flex-1"
          onClick={() => onClick && onClick(book)}
        >
          {/* Upper gradient section */}
          <div
            className="gradient-section h-[41px] rounded-t-lg"
            style={gradientStyle}
            aria-hidden="true"
          />
          {/* Lower text section */}
          <div
            className="text-section p-[10px_12px] rounded-b-lg flex-1 flex flex-col justify-center"
            style={{
              backgroundColor: mixedBgColor,
              color: 'var(--text-color)'
            }}
          >
            <h3 className="m-0 text-base font-medium leading-snug">
              {book.title}
            </h3>
            {book.author !== 'Unknown' && (
              <p className="mt-1 mb-0 text-[0.8rem] opacity-80">
                {book.author}
              </p>
            )}
          </div>
        </div>
      ) : (
        <Link
          to={navigationPath}
          className="book-card w-full p-0 m-0 rounded-lg overflow-hidden cursor-pointer bg-transparent flex flex-col flex-1 no-underline"
          aria-label={`${book.title}${book.author !== 'Unknown' ? `, Yazar: ${book.author}` : ''}`}
          onClick={() => onClick && onClick(book)}
        >
          {/* Upper gradient section */}
          <div
            className="gradient-section h-[41px] rounded-t-lg"
            style={gradientStyle}
            aria-hidden="true"
          />
          {/* Lower text section */}
          <div
            className="text-section p-[10px_12px] rounded-b-lg flex-1 flex flex-col justify-center"
            style={{
              backgroundColor: mixedBgColor,
              color: 'var(--text-color)'
            }}
          >
            <h3 className="m-0 text-base font-medium leading-snug">
              {book.title}
            </h3>
            {book.author !== 'Unknown' && (
              <p className="mt-1 mb-0 text-[0.8rem] opacity-80">
                {book.author}
              </p>
            )}
          </div>
        </Link>
      )}
    </div>
  );
};

export default memo(BookCard);
export { BookCard }; 