import { ReactNode, memo, HTMLAttributes } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ContentCardProps {
  title: string;
  subtitle?: string | ReactNode;
  metadata?: string | ReactNode;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

const ContentCard = ({ 
  title, 
  subtitle, 
  metadata, 
  onClick, 
  className = '',
  style = {}
}: ContentCardProps & HTMLAttributes<HTMLButtonElement>) => {
  const cardBgColor = useAutoOverlay(15, 'var(--bg-color)');
  
  return (
    <button
      onClick={onClick}
      className={`block w-full h-full text-left rounded-lg overflow-hidden transition-all hover:-translate-y-1 hover:shadow-lg cursor-pointer flex flex-col justify-center min-h-[60px] shadow-md p-2.5 ${className}`}
      style={{ 
        backgroundColor: cardBgColor,
        ...style 
      }}
    >
      <div className="flex justify-between items-start gap-4">
        <h3 className="m-0 text-[0.95rem] font-medium leading-snug text-[var(--text-color)]">
          {title}
        </h3>
        {subtitle && (
          <div className="text-[0.9rem] opacity-80 text-right text-[var(--text-color)]">
            {subtitle}
          </div>
        )}
      </div>
      {metadata && (
        <div className="flex justify-between items-end mt-2 text-xs opacity-60 text-[var(--text-color)]">
          {metadata}
        </div>
      )}
    </button>
  );
};

export default memo(ContentCard);
export { ContentCard }; 