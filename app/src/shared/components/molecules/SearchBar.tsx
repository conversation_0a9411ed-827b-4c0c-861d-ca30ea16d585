import { useState, ChangeEvent, memo } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ISearchBarProps {
  /** <PERSON><PERSON> yapıldığında çağrılacak fonksiyon */
  onSearch: (query: string) => void;
  /** Başlangıç arama terimi */
  initialSearchTerm?: string;
  /** Placeholder metni */
  placeholder?: string;
  /** Arama sonuçları yüklenirken gösterilecek yükleme durumu */
  isLoading?: boolean;
  /** Özel CSS sınıfı */
  className?: string;
}

const SearchBar = ({ 
  onSearch, 
  initialSearchTerm = '',
  placeholder = 'Kitap ara...',
  isLoading = false,
  className = '' 
}: ISearchBarProps) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const searchBarBgColor = useAutoOverlay(5, 'var(--bg-color)');

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value.toLowerCase());
  };

  return (
    <div className={`relative w-full ${className}`}>
      <input
        type="text"
        value={searchTerm}
        onChange={handleChange}
        placeholder={placeholder}
        className="w-full px-4 py-2 pl-10 rounded-lg text-base [&::placeholder]:text-[var(--text-color)] [&::placeholder]:opacity-70 text-[var(--text-color)]"
        style={{ 
          backgroundColor: searchBarBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
        disabled={isLoading}
        aria-label="Arama kutusu"
      />
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="18" 
        height="18" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        className="absolute left-3 top-1/2 transform -translate-y-1/2 opacity-60 text-[var(--text-color)]"
        aria-hidden="true"
      >
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </svg>
      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <svg
            className="animate-spin h-5 w-5 text-[var(--text-color)]"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      )}
    </div>
  );
};

export default memo(SearchBar);
export { SearchBar }; 