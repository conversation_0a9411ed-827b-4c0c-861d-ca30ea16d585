import { ReactNode, memo, FC } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import BackButton from '../../atoms/Button/BackButton';
import AppLogo from '../../atoms/Branding/AppLogo';
import ThemeToggle from '../../atoms/Button/ThemeToggle';
import LoginButton from '../../atoms/Button/LoginButton';
import MoreOptionsButton from '@shared/components/atoms/Button/MoreOptionsButton';

interface AppNavbarProps {
  title?: string;
  actions?: ReactNode;
  centerContent?: ReactNode;
  secondRowContent?: ReactNode; // İkinci satır içeriği
  showBackButton?: boolean;
  showLogo?: boolean;
  showThemeToggle?: boolean;
  showMoreOptions?: boolean;
  showLoginButton?: boolean;
  bgColorMixPercentage?: number;
  navbarTwoRows?: boolean; // İki satırlı navbar özelliği
}

const AppNavbar: FC<AppNavbarProps> = ({
  title,
  actions,
  centerContent,
  secondRowContent,
  showBackButton = true,
  showLogo = true,
  showThemeToggle = true,
  showMoreOptions = true,
  showLoginButton = true,
  navbarTwoRows = false
}: AppNavbarProps) => {
  const navigate = useNavigate();

  const navBgColor = useAutoOverlay(10, 'var(--bg-color)');

  return (
    <>
    <nav
      id="page-layout-navbar"
      className="fixed top-0 z-40 w-full shadow-md"
      style={{ 
        backgroundColor: navBgColor, 
        borderBottom: `1px solid color-mix(in srgb, var(--text-color) 10%, transparent)`
      }}
    >
      <div className="w-full mx-auto px-4 md:px-6 lg:px-8">
        {/* Üst satır - Ana navbar içeriği */}
        <div className="relative flex items-center h-10">
          {/* Sol Kısım */}
          <div className="absolute left-0 flex items-center justify-start h-full">
            {showBackButton && <BackButton onClick={() => navigate(-1)} />}
            {showLogo && <AppLogo />}
          </div>

          {/* Orta Kısım (Absolute ile tam ortalama) */}
          <div className="flex items-center justify-center h-full w-full">
            {centerContent ? (
              centerContent
            ) : title ? (
              <h1 className="text-base font-medium truncate px-2 m-0 leading-none" style={{ color: 'var(--text-color)' }}>
                {title}
              </h1>
            ) : null}
          </div>

          {/* Sağ Kısım (Aksiyonlar) */}
          <div className="absolute right-0 flex items-center justify-end space-x-1 h-full">
            {actions}
              {showLoginButton && <LoginButton />}
            {showThemeToggle && <ThemeToggle />}
            {showMoreOptions && (
              <div className="relative">
                <MoreOptionsButton />
              </div>
            )}
          </div>
        </div>
        
        {/* İkinci satır - Sadece navbarTwoRows true ise göster */}
        {navbarTwoRows && secondRowContent && (
          <div 
            id="page-layout-second-row"
            className="flex items-center justify-center h-6 pb-1 -mt-1">
            {secondRowContent}
          </div>
        )}
      </div>
    </nav>
    </>
  );
};

export default memo(AppNavbar);
export { AppNavbar }; 