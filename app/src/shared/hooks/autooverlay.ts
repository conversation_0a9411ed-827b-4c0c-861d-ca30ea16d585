/*
 * autoOverlay Hook <PERSON><PERSON> ve <PERSON>:
 *
 * - app/src/shared/components/organisms/ExpandableSection.tsx:
 *   - headerBgColor: 4%
 *   - contentBgColor: 3%
 * - app/src/shared/components/molecules/BookCard.tsx:
 *   - mixedBgColor: 13%
 * - app/src/shared/components/atoms/Input/SearchBar.tsx:
 *   - bgColor: 5%
 * - app/src/shared/components/molecules/SearchBar.tsx:
 *   - searchBarBgColor: 5%
 * - app/src/shared/components/molecules/Navigation/AppNavbar.tsx:
 *   - navBgColor: 10% (Sabit)
 * - app/src/shared/components/molecules/Cards/ContentCard.tsx:
 *   - cardBgColor: 15%
 * - app/src/domains/reader/pages/quran/Components/NavigationSheet.tsx:
 *   - navBgColor: 8%
 *   - toolbarBgColor: 4%
 * - app/src/domains/reader/pages/quran/ReadPage.tsx:
 *   - navBgColor: 8%
 */
import { useState, useEffect } from 'react';

/**
 * Determines if a color is light or dark
 */
const isLightColor = (color: string): boolean => {
  try {
    // Get CSS variable
    const root = document.documentElement;
    const computedColor = getComputedStyle(root).getPropertyValue(color.replace('var(', '').replace(')', '')).trim();
    
    // Convert to hex
    let hex = computedColor;
    if (computedColor.startsWith('#')) {
      hex = computedColor.slice(1);
    } else if (computedColor.startsWith('rgb')) {
      const rgb = computedColor.match(/\d+/g);
      if (rgb) {
        hex = rgb.map(x => parseInt(x).toString(16).padStart(2, '0')).join('');
      }
    }

    // Calculate brightness (YIQ formula)
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    
    return yiq >= 128; // Light if > 128
  } catch (error) {
    console.error('Color calculation error:', error);
    return true; // Default to light in case of error
  }
};

/**
 * Hook for mixing colors with a CSS color-mix function
 * @param percentage - Percentage to mix (0-100)
 * @param bgColor - Base color (usually a CSS variable)
 * @returns The mixed color string
 */
export const useAutoOverlay = (percentage: number, bgColor: string): string => {
  const [mixedColor, setMixedColor] = useState<string>('');

  useEffect(() => {
    // Callback for MutationObserver
    const updateColor = () => {
      const isLight = isLightColor(bgColor);
      const mixWithColor = isLight ? 'black' : 'white';
      setMixedColor(`color-mix(in srgb, ${bgColor} ${100 - percentage}%, ${mixWithColor})`);
    };

    // Initial color calculation
    updateColor();

    // Watch for CSS variable changes
    const observer = new MutationObserver(updateColor);
    
    // Observe style changes on the root element
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    // Cleanup
    return () => observer.disconnect();
  }, [percentage, bgColor]);

  return mixedColor;
}; 