// app/src/utils/dataFetcher.ts

// Base URL for local data (can be configured elsewhere if needed)
const LOCAL_DATA_BASE_URL = '/data';

// Cache Map - shared across all calls to fetchData in this module instance
const cache = new Map<string, unknown>();

/**
 * Fetches JSON data from a specified path relative to the /data directory,
 * utilizing an in-memory cache to avoid redundant requests.
 *
 * @param relativePath The path to the JSON file relative to /data (e.g., '/library/books.json').
 * @returns A promise that resolves with the parsed JSON data of type T.
 * @throws An error if the fetch fails or the response is not ok.
 */
export async function fetchData<T>(relativePath: string): Promise<T> {
  const fullUrl = `${LOCAL_DATA_BASE_URL}${relativePath}`;

  // 1. Check cache
  if (cache.has(fullUrl)) {
    console.log(`[fetchData] Cache hit for: ${fullUrl}`);
    // Return a new promise that resolves with the cached data
    // to maintain async behavior consistency, although direct return is also possible.
    return Promise.resolve(cache.get(fullUrl) as T);
  }

  console.log(`[fetchData] Cache miss, fetching: ${fullUrl}`);

  // 2. Fetch from network if not in cache
  try {
    const response = await fetch(fullUrl);

    // 3. Check response status
    if (!response.ok) {
      // Attempt to read error body for more context if possible
      let errorBody = '';
      try {
        errorBody = await response.text();
      } catch {
        // Ignore if reading text fails
      }
      console.error(`[fetchData] HTTP error fetching ${fullUrl}. Status: ${response.status}. Body: ${errorBody}`);
      throw new Error(`Failed to fetch ${fullUrl}. Status: ${response.status}`);
    }

    // 4. Parse JSON
    const data = await response.json();

    // 5. Add to cache
    cache.set(fullUrl, data);
    console.log(`[fetchData] Successfully fetched and cached: ${fullUrl}`);

    // 6. Return data
    return data as T;

  } catch (error) {
    // Log the error and re-throw to be caught by the caller
    console.error(`[fetchData] Error during fetch or parse for ${fullUrl}:`, error);
    // Re-throw the original error or a new one for consistent error handling
    if (error instanceof Error) {
      throw error; // Re-throw the original error
    } else {
      throw new Error(`An unknown error occurred while fetching ${fullUrl}`);
    }
  }
}

// Optional: Function to clear the cache if needed for specific scenarios
export function clearDataCache() {
  console.log('[fetchData] Clearing data cache.');
  cache.clear();
}

// Optional: Function to invalidate a specific cache entry
export function invalidateCacheEntry(relativePath: string) {
   const fullUrl = `${LOCAL_DATA_BASE_URL}${relativePath}`;
   if (cache.has(fullUrl)) {
     cache.delete(fullUrl);
     console.log(`[fetchData] Invalidated cache for: ${fullUrl}`);
     return true;
   }
   return false;
} 