import { fetchJsonFromR2 } from './r2Client';

// Cache Map - shared across all calls to fetchDataFromR2 in this module instance
const cache = new Map<string, unknown>();

// Paralel istekleri takip etmek için bir harita
const pendingRequests = new Map<string, Promise<unknown>>();

// Retry yapılandırması
const MAX_RETRIES = 2;
const RETRY_DELAY = 1000; // ms

// Hata türleri
export enum R2FetchErrorType {
  NETWORK = 'network',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  PARSE = 'parse',
  UNKNOWN = 'unknown'
}

// Özelleştirilmiş hata sınıfı
export class R2FetchError extends Error {
  type: R2FetchErrorType;
  path: string;
  statusCode?: number;
  originalError?: Error;

  constructor(
    message: string,
    type: R2FetchErrorType,
    path: string,
    statusCode?: number,
    originalError?: Error
  ) {
    super(message);
    this.name = 'R2FetchError';
    this.type = type;
    this.path = path;
    this.statusCode = statusCode;
    this.originalError = originalError;
  }
}

/**
 * Belirli bir süre bekleyen yardımcı fonksiyon
 */
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Fetches JSON data from Cloudflare R2 via Worker,
 * utilizing an in-memory cache to avoid redundant requests.
 *
 * @param path The path to the JSON file in R2 storage.
 * @param retryCount İç kullanım için, şu anki yeniden deneme sayısı
 * @returns A promise that resolves with the parsed JSON data of type T.
 * @throws An error if the fetch fails or the response is not ok.
 */
export async function fetchDataFromR2<T>(path: string, retryCount = 0): Promise<T> {
  // 1. Check cache
  if (cache.has(path)) {
    console.log(`[fetchDataFromR2] Cache hit for: ${path}`);
    // Return a new promise that resolves with the cached data
    return Promise.resolve(cache.get(path) as T);
  }

  // 2. Zaten devam eden bir istek var mı kontrol et
  if (pendingRequests.has(path)) {
    console.log(`[fetchDataFromR2] Request already pending for: ${path}, waiting for result`);
    return pendingRequests.get(path) as Promise<T>;
  }

  console.log(`[fetchDataFromR2] Cache miss, fetching from R2: ${path}${retryCount > 0 ? ` (retry ${retryCount}/${MAX_RETRIES})` : ''}`);

  // 3. Yeni isteği başlat ve pending listesine ekle
  const fetchPromise = (async () => {
    try {
      // Use r2Client to fetch the data
      const data = await fetchJsonFromR2<T>(path);

      // Add to cache
      cache.set(path, data);
      console.log(`[fetchDataFromR2] Successfully fetched and cached: ${path}`);

      // İsteği tamamlandıktan sonra pending listesinden çıkar
      pendingRequests.delete(path);

      // Return data
      return data;
    } catch (error) {
      // İstek hata ile sonuçlanırsa da pending listesinden çıkar
      pendingRequests.delete(path);

      // Log the error
      console.error(`[fetchDataFromR2] Error during fetch for ${path}:`, error);

      // Retry logic
      if (retryCount < MAX_RETRIES) {
        console.log(`[fetchDataFromR2] Retrying fetch for ${path} (${retryCount + 1}/${MAX_RETRIES})...`);
        await sleep(RETRY_DELAY * (retryCount + 1)); // Exponential backoff
        return fetchDataFromR2<T>(path, retryCount + 1);
      }

      // Max retries exceeded, hata türünü belirle ve özelleştirilmiş hata fırlat
      let errorType = R2FetchErrorType.UNKNOWN;
      let statusCode: number | undefined = undefined;
      let errorMessage = `İçerik yüklenirken bir hata oluştu: ${path}`;

      if (error instanceof Error) {
        // Hata mesajından HTTP durum kodunu çıkarmaya çalış
        const statusMatch = error.message.match(/status (\d+)/i);
        if (statusMatch && statusMatch[1]) {
          statusCode = parseInt(statusMatch[1], 10);
        }

        // Hata türünü belirle
        if (error.message.includes('Failed to fetch') ||
            error.message.includes('Network request failed') ||
            error.message.includes('network error')) {
          errorType = R2FetchErrorType.NETWORK;
          errorMessage = `Ağ bağlantısı hatası: ${path}`;
        } else if (statusCode === 404 || error.message.includes('not found')) {
          errorType = R2FetchErrorType.NOT_FOUND;
          errorMessage = `İçerik bulunamadı: ${path}`;
        } else if (statusCode && statusCode >= 500) {
          errorType = R2FetchErrorType.SERVER;
          errorMessage = `Sunucu hatası: ${path}`;
        } else if (error.message.includes('JSON')) {
          errorType = R2FetchErrorType.PARSE;
          errorMessage = `İçerik ayrıştırma hatası: ${path}`;
        }

        throw new R2FetchError(
          errorMessage,
          errorType,
          path,
          statusCode,
          error
        );
      } else {
        throw new R2FetchError(
          errorMessage,
          errorType,
          path
        );
      }
    }
  })();

  // Devam eden isteği kaydet
  pendingRequests.set(path, fetchPromise);

  // İsteği döndür
  return fetchPromise;
}

// Optional: Function to clear the cache if needed for specific scenarios
export function clearR2DataCache() {
  console.log('[fetchDataFromR2] Clearing R2 data cache.');
  cache.clear();
  // Pending isteklerin de temizlenmesi gerekip gerekmediği değerlendirilmeli
  // Bu, devam eden istekleri iptal etmez, sadece referansları siler.
  pendingRequests.clear();
}

// Optional: Function to invalidate a specific cache entry
export function invalidateR2CacheEntry(path: string) {
   if (cache.has(path)) {
     cache.delete(path);
     console.log(`[fetchDataFromR2] Invalidated cache for: ${path}`);
     return true;
   }
   return false;
}