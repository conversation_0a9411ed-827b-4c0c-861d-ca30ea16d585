import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { useEffect, useState, Fragment } from 'react';
import { PageLayout, SearchBar, AppLogo, ThemeToggle, LoginButton, MoreOptionsButton, SavedContentAccessCard } from '@shared/components';
import { ExpandableSection } from './shared/components/organisms/ExpandableSection';
import { ThemeProvider } from './shared/context/ThemeContext';
import QuranChaptersPage from './domains/reader/pages/quran/ChaptersPage';
import QuranReadPage from './domains/reader/pages/quran/ReadPage';
import RisaleReadPage from './domains/reader/pages/risale/ReadPage';
import RisaleChapterSelectionPage from './domains/reader/pages/risale/ChapterSelection';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { AnnotationSearchPage } from './domains/reader-interactions/annotations/pages/AnnotationSearchPage';
import { AnnotationDetailPage } from './domains/reader-interactions/annotations/pages/AnnotationDetailPage';
import SavedContentPage from './domains/reader-interactions/bookmarks/pages/SavedContentPage';
import { TopicListPage, TopicDetailPage } from './domains/topics';
import { useLibraryStore } from './domains/library/store/librarystore';
import { LoadingState } from './shared/components/molecules/Feedback/LoadingState';
import { useAuthStore } from './domains/auth/store/authStore';
import ResetPasswordPage from './domains/auth/pages/ResetPasswordPage';
import UsernameSetupPage from './domains/auth/pages/UsernameSetupPage';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { useAuth } from '@domains/auth/hooks/useAuth';
import { AuthSheet } from '@domains/auth/pages/Components/AuthSheet';
import { ProfileSheet } from '@domains/auth/pages/Components/ProfileSheet';
// Hata yönetimi bileşenleri
import ErrorBoundary from '@shared/components/organisms/ErrorBoundary/ErrorBoundary';
import NetworkStatusMonitor from '@shared/components/organisms/NetworkStatusMonitor/NetworkStatusMonitor';

// Annotation Service ve hook'ları
import { AnnotationService } from './domains/reader-interactions/services/AnnotationService';
import { useAnnotationManager } from './domains/reader-interactions/annotations/hooks/useAnnotationManager';


// ✅ YENİ ORGANİZE YAPISI - Topic sistemi
import { TopicDiscoveryCard } from './domains/topics';

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const { openLoginModal } = useAuth();

  // State'leri ayrı ayrı seç (ince kontrol ve gereksiz yeniden render'ları önlemek için)
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isUsernameSetupRequired = useAuthStore((state) => state.isUsernameSetupRequired);
  const isLoading = useAuthStore((state) => state.isLoading);
  const user = useAuthStore((state) => state.user);

  useEffect(() => {
    // Auth yükleniyor ve kararlar henüz verilememiş ise bekle
    if (isLoading) {
      console.log('[AuthRedirectManager] Auth state loading, waiting...');
      return;
    }

    const currentPath = window.location.pathname;

    // Eğer kullanıcı girişi var ve username ekranına ihtiyaç varsa, gerekli yönlendirmeyi yap
    if (isAuthenticated && isUsernameSetupRequired) {
      if (currentPath !== '/profil-tamamla') {
        console.log('[AuthRedirectManager] Username setup required, redirecting to setup page');
        navigate('/profil-tamamla', { replace: true });
      }
    }
    // Eğer kullanıcı username sayfasında ama username ayarlanmış ise ana sayfaya yönlendir
    else if (currentPath === '/profil-tamamla' && isAuthenticated && !isUsernameSetupRequired) {
      console.log('[AuthRedirectManager] Username already set, redirecting from setup page to home');
      navigate('/', { replace: true });
    }
    // Sadece hata ayıklama için
    else {
      console.log('[AuthRedirectManager] No redirects needed. Auth state:', {
        isAuthenticated,
        isUsernameSetupRequired,
        currentPath,
        provider: user?.app_metadata?.provider
      });
    }
  }, [isAuthenticated, isUsernameSetupRequired, isLoading, navigate, location.pathname, user?.app_metadata?.provider]);

  // Annotation service'i initialize etmek için hook'ları çağır
  const { createAnnotation, getAnnotations } = useAnnotationManager();

  // App ilk yüklendiğinde servisi initialize et
  useEffect(() => {
    AnnotationService.initialize(createAnnotation, getAnnotations);
  }, [createAnnotation, getAnnotations]);

  // Home page component
  const HomePage = () => {
    const isMobile = useIsMobile();
    

    // Get state and actions from the library store
    const books = useLibraryStore((state) => state.books);
    const categories = useLibraryStore((state) => state.categories);
    const fetchBooks = useLibraryStore((state) => state.fetchBooks);
    const fetchCategories = useLibraryStore((state) => state.fetchCategories);
    const loadingBooks = useLibraryStore((state) => state.loading.books);
    const loadingCategories = useLibraryStore((state) => state.loading.categories);

    // Auth state
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

    // State for search term
    const [searchTerm, setSearchTerm] = useState('');

    // Fetch data on component mount
    useEffect(() => {
      fetchBooks();
      fetchCategories();
    }, [fetchBooks, fetchCategories]);

    // Group books by category using data from the store
    const booksByCategory = categories.map(category => {
      const categoryBooks = books.filter(book => book.category_id === category.id);
      return {
        category,
        books: categoryBooks
      };
    });

    // Define Navbar Center Content with Besmele
    const homeNavbarCenter = (
      <p className="text-base font-arabic m-0 leading-none" style={{ color: 'var(--text-color)' }}>
        بِسْمِ اللّٰهِ الرَّحْمٰنِ الرَّح۪يمِ
      </p>
    );

    // Define Navbar Second Row Content for Mobile
    const homeNavbarSecondRow = isMobile ? (
      <div className="flex items-center justify-between w-full px-2 h-full">
        <AppLogo />
        <div className="flex items-center space-x-1 sm:space-x-2">
          <LoginButton onClick={openLoginModal} />
          <ThemeToggle />
          <div className="relative">
            <MoreOptionsButton />
          </div>
        </div>
      </div>
    ) : null;

    // Navbar Actions removed

    // Filter logic (basic example - apply actual filtering later)
    const filteredBooksByCategory = booksByCategory.filter(item =>
        item.category.name.toLowerCase().includes(searchTerm.toLowerCase())
        // Add book title filtering later if needed
    );

    if (loadingBooks || loadingCategories) {
      return (
        <PageLayout
          navbarCenterContent={homeNavbarCenter}
          secondRowContent={homeNavbarSecondRow}
          navbarTwoRows={isMobile}
          showBackButton={false}
          showLogo={!isMobile}
          showThemeToggle={!isMobile}
          showLoginButton={!isMobile}
          showMoreOptions={!isMobile}
        >
          <main className="container mx-auto px-4 py-8 flex justify-center items-center">
            <LoadingState message="Kitaplar yükleniyor..." />
          </main>
        </PageLayout>
      );
    }

    return (
      <Fragment>
        <PageLayout
          navbarCenterContent={homeNavbarCenter}
          secondRowContent={homeNavbarSecondRow}
          navbarTwoRows={isMobile}
          showBackButton={false}
          showLogo={!isMobile}
          showThemeToggle={!isMobile}
          showLoginButton={!isMobile}
          showMoreOptions={!isMobile}
        >
          <main className="container mx-auto px-4 py-8">
            <div className="max-w-xl mx-auto">
              <SearchBar
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder="Kitap veya kategori ara..."
                className="mb-6"
              />

              <div className="grid grid-cols-2 gap-4 mb-6">
                {/* Kaydetme Kartı - sadece giriş yapan kullanıcılar için */}
                {isAuthenticated && (
                  <SavedContentAccessCard />
                )}

                {/* Topic Keşif Kartı - herkese açık */}
                <TopicDiscoveryCard />
              </div>
            </div>

            <div className="space-y-6">
              {filteredBooksByCategory.map(({ category, books }) => (
                <ExpandableSection
                  key={category.id}
                  title={category.name}
                  items={books}
                  category={category}
                  initialExpanded={true}
                  className="mb-4"
                />
              ))}
            </div>
          </main>
        </PageLayout>
      </Fragment>
    );
  };

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // Hata izleme servisi entegrasyonu buraya eklenebilir
        console.error('Application Error:', error, errorInfo);
      }}
    >
      <ThemeProvider>
        <NetworkStatusMonitor
          offlineMessage="İnternet bağlantınız kesildi. İçerik yükleme ve diğer çevrimiçi özellikler çalışmayabilir."
        >
          <div className="min-h-screen bg-[var(--bg-color)]">
            <Routes>
              {/* Homepage */}
              <Route path="/" element={
                <ErrorBoundary>
                  <HomePage />
                </ErrorBoundary>
              } />

              {/* Profil Tamamlama Sayfası */}
              <Route path="/profil-tamamla" element={
                <ErrorBoundary>
                  <UsernameSetupPage />
                </ErrorBoundary>
              } />

              {/* Şifre Sıfırlama Sayfası */}
              <Route path="/reset-password" element={
                <ErrorBoundary>
                  <ResetPasswordPage />
                </ErrorBoundary>
              } />

              {/* Kuran Sayfaları (Chapter Listesi) */}
              <Route path="/kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/mealli-kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/kelime-mealli-kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran-meali-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />

              {/* Kuran Okuma Sayfaları */}
              <Route path="/kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran/oku/:bookId/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/mealli-kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kelime-mealli-kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran-meali/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />

              {/* Risale Sayfaları */}
              <Route path="/risale/:bookId" element={
                <ErrorBoundary>
                  <RisaleChapterSelectionPage />
                </ErrorBoundary>
              } />
              <Route path="/risale/:bookId/:sectionId" element={
                <ErrorBoundary>
                  <RisaleReadPage />
                </ErrorBoundary>
              } />

              {/* Annotation Search Page */}
              <Route path="/annotations/search" element={
                <ErrorBoundary>
                  <AnnotationSearchPage />
                </ErrorBoundary>
              } />

              {/* Annotation Detail Page */}
              <Route path="/annotations/:annotationId" element={
                <ErrorBoundary>
                  <AnnotationDetailPage />
                </ErrorBoundary>
              } />

              {/* Saved Content Page */}
              <Route path="/saved-content" element={
                <ErrorBoundary>
                  <SavedContentPage />
                </ErrorBoundary>
              } />
              {/* Türkçe URL için redirect */}
              <Route path="/kaydettiğim-icerikler" element={
                <ErrorBoundary>
                  <SavedContentPage />
                </ErrorBoundary>
              } />

              {/* Topic Pages */}
              <Route path="/topics" element={
                <ErrorBoundary>
                  <TopicListPage />
                </ErrorBoundary>
              } />
              <Route path="/topics/:topicId" element={
                <ErrorBoundary>
                  <TopicDetailPage />
                </ErrorBoundary>
              } />
            </Routes>
            <AuthSheet />
            <ProfileSheet />
          </div>
        </NetworkStatusMonitor>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
