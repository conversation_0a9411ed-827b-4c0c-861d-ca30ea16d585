import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { PageLayout, LoadingState } from '@shared/components';
import { BookOpen, Clock, Tag, ArrowLeft, ExternalLink } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { topicService } from '../services/topicService';
import type { Topic } from '../models/types';

// Örnek içerik verileri
const SAMPLE_CONTENT = {
  "tevhid": [
    {
      "sentence_id": "5_1_15",
      "book_title": "<PERSON><PERSON>zler",
      "section": "<PERSON><PERSON>nc<PERSON>",
      "text": "<PERSON><PERSON>, kâinatın her zerresinde tevhidin delilleri vardır. Her atom, Hâlık'ının birliğine şehadet eder.",
      "context": "Tevhidin kâinattaki delilleri",
      "relevance_score": 0.95
    },
    {
      "sentence_id": "7_3_42",
      "book_title": "Lem'alar",
      "section": "Üçüncü Lem'a",
      "text": "Tevhid, bütün hakikatlerin anahtarıdır. O olmadan hiçbir hakikat anlaşılamaz.",
      "context": "Tevhidin önemi",
      "relevance_score": 0.92
    },
    {
      "sentence_id": "12_2_28",
      "book_title": "Şuâlar",
      "section": "İkinci Şuâ",
      "text": "Lâ ilâhe illallah kelimesi, kâinatın en büyük hakikatidir ve en yüksek zikiridir.",
      "context": "Kelime-i tevhidin önemi",
      "relevance_score": 0.89
    }
  ],
  "iman-esaslari": [
    {
      "sentence_id": "5_2_8",
      "book_title": "Sözler",
      "section": "İkinci Söz",
      "text": "İmanın altı esası, insanın saadetinin altı direğidir. Bunlar olmadan ruh huzur bulamaz.",
      "context": "İman esaslarının önemi",
      "relevance_score": 0.94
    },
    {
      "sentence_id": "6_1_12",
      "book_title": "Mektubat",
      "section": "Birinci Mektup",
      "text": "Allah'a iman, kalbin nurudur. Meleklere iman, ruhun kanadıdır.",
      "context": "İmanın manevi boyutu",
      "relevance_score": 0.88
    }
  ],
  "namaz": [
    {
      "sentence_id": "8_4_15",
      "book_title": "Sözler",
      "section": "Dördüncü Söz",
      "text": "Namaz, mü'minin miracıdır. O, namazda Rabbine yaklaşır ve O'nunla münacaat eder.",
      "context": "Namazın manevi boyutu",
      "relevance_score": 0.96
    },
    {
      "sentence_id": "11_2_33",
      "book_title": "Lem'alar",
      "section": "İkinci Lem'a",
      "text": "Beş vakit namaz, günün beş mevsimi gibidir. Her biri ayrı bir rahmet kapısıdır.",
      "context": "Namazın hikmeti",
      "relevance_score": 0.91
    }
  ],
  "sabir": [
    {
      "sentence_id": "10_3_22",
      "book_title": "Mektubat",
      "section": "Üçüncü Mektup",
      "text": "Sabır, iman ile küfür arasında bir kale gibidir. Sabırlı olan, imanını muhafaza eder.",
      "context": "Sabrın imanı koruyucu rolü",
      "relevance_score": 0.93
    },
    {
      "sentence_id": "17_1_45",
      "book_title": "Şuâlar",
      "section": "Birinci Şuâ",
      "text": "Musibetlerde sabır, nimette şükür; ikisi de Cennet anahtarlarıdır.",
      "context": "Sabır ve şükrün önemi",
      "relevance_score": 0.89
    }
  ],
  "marifet": [
    {
      "sentence_id": "7_5_18",
      "book_title": "Lem'alar",
      "section": "Beşinci Lem'a",
      "text": "Marifet, kalbin gözüdür. O gözle Allah'ın esma ve sıfatları müşahede edilir.",
      "context": "Marifetin hakikati",
      "relevance_score": 0.97
    },
    {
      "sentence_id": "12_4_31",
      "book_title": "Şuâlar",
      "section": "Dördüncü Şuâ",
      "text": "Her mahluk, Hâlık'ının bir isminin aynasıdır. Marifet ehli bu aynaları okur.",
      "context": "Kâinatta marifet",
      "relevance_score": 0.94
    }
  ]
};

const TopicDetailPage: React.FC = () => {
  const { topicId } = useParams<{ topicId: string }>();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [topic, setTopic] = useState<Topic | null>(null);
  const [loading, setLoading] = useState(true);

  // Auto overlay colors
  const cardBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const sectionBgColor = useAutoOverlay(4, 'var(--bg-color)');

  useEffect(() => {
    const loadTopic = async () => {
      if (!topicId) return;
      
      try {
        const topicData = await topicService.getTopicById(topicId);
        setTopic(topicData);
      } catch (error) {
        console.error('Topic yüklenemedi:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTopic();
  }, [topicId]);

  const handleBackClick = () => {
    navigate('/topics');
  };

  const handleReadInContext = (sentenceId: string) => {
    // Gelecekte: ilgili kitap sayfasına yönlendir
    console.log('Read in context:', sentenceId);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600';
      case 'intermediate': return 'text-yellow-600';
      case 'advanced': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'Başlangıç';
      case 'intermediate': return 'Orta';
      case 'advanced': return 'İleri';
      default: return difficulty;
    }
  };

  if (loading) {
    return (
      <PageLayout
        showBackButton={true}
        showLogo={!isMobile}
        showThemeToggle={!isMobile}
        showLoginButton={!isMobile}
        showMoreOptions={!isMobile}
      >
        <main className="container mx-auto px-4 py-8">
          <LoadingState message="Konu yükleniyor..." />
        </main>
      </PageLayout>
    );
  }

  if (!topic) {
    return (
      <PageLayout
        showBackButton={true}
        showLogo={!isMobile}
        showThemeToggle={!isMobile}
        showLoginButton={!isMobile}
        showMoreOptions={!isMobile}
      >
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-[var(--text-color)] mb-4">
              Konu Bulunamadı
            </h1>
            <button
              onClick={handleBackClick}
              className="text-blue-600 hover:text-blue-700"
            >
              ← Konulara Dön
            </button>
          </div>
        </main>
      </PageLayout>
    );
  }

  // Örnek içeriği al
  const sampleContent = SAMPLE_CONTENT[topicId as keyof typeof SAMPLE_CONTENT] || [];

  return (
    <PageLayout
      showBackButton={true}
      showLogo={!isMobile}
      showThemeToggle={!isMobile}
      showLoginButton={!isMobile}
      showMoreOptions={!isMobile}
    >
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <button
            onClick={handleBackClick}
            className="flex items-center space-x-2 text-[var(--text-color)]/70 hover:text-[var(--text-color)] mb-6 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Konulara Dön</span>
          </button>

          {/* Topic Header */}
          <div 
            className="rounded-xl p-6 mb-8 border border-[var(--text-color)]/10"
            style={{ backgroundColor: cardBgColor }}
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-[var(--text-color)] mb-2">
                  {topic.title}
                </h1>
                <p className="text-[var(--text-color)]/70 text-lg">
                  {topic.description}
                </p>
              </div>
              <Tag className="w-8 h-8 text-blue-500 flex-shrink-0" />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <BookOpen className="w-4 h-4 text-blue-500" />
                  <span className="text-xl font-bold text-[var(--text-color)]">
                    {topic.sentence_count}
                  </span>
                </div>
                <p className="text-xs text-[var(--text-color)]/60">Paragraf</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Clock className="w-4 h-4 text-green-500" />
                  <span className="text-xl font-bold text-[var(--text-color)]">
                    {topic.estimated_reading_time}
                  </span>
                </div>
                <p className="text-xs text-[var(--text-color)]/60">Dakika</p>
              </div>

              <div className="text-center">
                <span className={`text-sm font-medium ${getDifficultyColor(topic.difficulty)}`}>
                  {getDifficultyText(topic.difficulty)}
                </span>
                <p className="text-xs text-[var(--text-color)]/60">Seviye</p>
              </div>

              <div className="text-center">
                <span className="text-xl font-bold text-[var(--text-color)]">
                  {topic.book_coverage.length}
                </span>
                <p className="text-xs text-[var(--text-color)]/60">Kitap</p>
              </div>
            </div>

            {/* Keywords */}
            <div className="flex flex-wrap gap-2">
              {topic.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="px-3 py-1 rounded-full text-xs font-medium text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </div>

          {/* Sample Content */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[var(--text-color)] mb-6">
              Örnek İçerik
            </h2>
            
            {sampleContent.length > 0 ? (
              <div className="space-y-6">
                {sampleContent.map((content, index) => (
                  <div
                    key={index}
                    className="rounded-lg p-6 border border-[var(--text-color)]/10"
                    style={{ backgroundColor: sectionBgColor }}
                  >
                    {/* Content Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2 text-sm text-[var(--text-color)]/60">
                        <BookOpen className="w-4 h-4" />
                        <span>{content.book_title}</span>
                        <span>•</span>
                        <span>{content.section}</span>
                      </div>
                      <button
                        onClick={() => handleReadInContext(content.sentence_id)}
                        className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-700 transition-colors"
                      >
                        <ExternalLink className="w-3 h-3" />
                        <span>Bağlamında Oku</span>
                      </button>
                    </div>

                    {/* Content Text */}
                    <p className="text-[var(--text-color)] text-lg leading-relaxed mb-3">
                      "{content.text}"
                    </p>

                    {/* Context */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[var(--text-color)]/60">
                        {content.context}
                      </span>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span className="text-xs text-[var(--text-color)]/60">
                          %{Math.round(content.relevance_score * 100)} ilgili
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <BookOpen className="w-12 h-12 text-[var(--text-color)]/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-[var(--text-color)] mb-2">
                  Örnek İçerik Hazırlanıyor
                </h3>
                <p className="text-[var(--text-color)]/60">
                  Bu konu için örnek içerikler yakında eklenecek
                </p>
              </div>
            )}
          </div>

          {/* Related Topics */}
          {topic.related_topics.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-[var(--text-color)] mb-6">
                İlgili Konular
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {topic.related_topics.map((relatedTopicId, index) => (
                  <button
                    key={index}
                    onClick={() => navigate(`/topics/${relatedTopicId}`)}
                    className="p-4 rounded-lg border border-[var(--text-color)]/10 hover:border-[var(--text-color)]/20 transition-colors text-left"
                    style={{ backgroundColor: sectionBgColor }}
                  >
                    <div className="flex items-center space-x-2">
                      <Tag className="w-4 h-4 text-blue-500" />
                      <span className="text-[var(--text-color)] font-medium capitalize">
                        {relatedTopicId.replace('-', ' ')}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
    </PageLayout>
  );
};

export default TopicDetailPage;
