import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout, SearchBar, LoadingState } from '@shared/components';
import { BookOpen, Clock, Tag, ArrowRight } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { topicService } from '../services/topicService';
import type { TopicIndex } from '../models/types';

const TopicListPage: React.FC = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [topicIndex, setTopicIndex] = useState<TopicIndex | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Auto overlay colors
  const cardBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const categoryBgColor = useAutoOverlay(4, 'var(--bg-color)');

  useEffect(() => {
    const loadTopics = async () => {
      try {
        const index = await topicService.getTopicIndex();
        setTopicIndex(index);
      } catch (error) {
        console.error('Topic index yüklenemedi:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTopics();
  }, []);

  // Filter topics based on search and category
  const filteredTopics = React.useMemo(() => {
    if (!topicIndex) return [];

    let topics = Object.values(topicIndex.topics);

    // Category filter
    if (selectedCategory !== 'all') {
      topics = topics.filter(topic => topic.category === selectedCategory);
    }

    // Search filter
    if (searchTerm) {
      const lowercaseQuery = searchTerm.toLowerCase();
      topics = topics.filter(topic => 
        topic.title.toLowerCase().includes(lowercaseQuery) ||
        topic.description.toLowerCase().includes(lowercaseQuery) ||
        topic.keywords.some(keyword => keyword.toLowerCase().includes(lowercaseQuery))
      );
    }

    return topics;
  }, [topicIndex, selectedCategory, searchTerm]);

  const handleTopicClick = (topicId: string) => {
    navigate(`/topics/${topicId}`);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600';
      case 'intermediate': return 'text-yellow-600';
      case 'advanced': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'Başlangıç';
      case 'intermediate': return 'Orta';
      case 'advanced': return 'İleri';
      default: return difficulty;
    }
  };

  if (loading) {
    return (
      <PageLayout
        showBackButton={true}
        showLogo={!isMobile}
        showThemeToggle={!isMobile}
        showLoginButton={!isMobile}
        showMoreOptions={!isMobile}
      >
        <main className="container mx-auto px-4 py-8">
          <LoadingState message="Konular yükleniyor..." />
        </main>
      </PageLayout>
    );
  }

  if (!topicIndex) {
    return (
      <PageLayout
        showBackButton={true}
        showLogo={!isMobile}
        showThemeToggle={!isMobile}
        showLoginButton={!isMobile}
        showMoreOptions={!isMobile}
      >
        <main className="container mx-auto px-4 py-8">
          <div className="text-center text-[var(--text-color)]/60">
            Konular yüklenemedi
          </div>
        </main>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      showBackButton={true}
      showLogo={!isMobile}
      showThemeToggle={!isMobile}
      showLoginButton={!isMobile}
      showMoreOptions={!isMobile}
    >
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-[var(--text-color)] mb-2">
              Konu Keşfi
            </h1>
            <p className="text-[var(--text-color)]/70">
              Risale-i Nur'dan tematik konuları keşfedin ve derinlemesine okuyun
            </p>
          </div>

          {/* Search */}
          <div className="mb-6">
            <SearchBar
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Konu ara..."
              className="max-w-md"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-8">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'text-[var(--text-color)]/70 hover:text-[var(--text-color)]'
              }`}
              style={selectedCategory !== 'all' ? { backgroundColor: categoryBgColor } : {}}
            >
              Tümü ({Object.keys(topicIndex.topics).length})
            </button>
            {Object.entries(topicIndex.categories).map(([key, category]) => (
              <button
                key={key}
                onClick={() => setSelectedCategory(key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === key
                    ? 'text-white'
                    : 'text-[var(--text-color)]/70 hover:text-[var(--text-color)]'
                }`}
                style={{
                  backgroundColor: selectedCategory === key ? category.color : categoryBgColor
                }}
              >
                {category.title} ({category.topics.length})
              </button>
            ))}
          </div>

          {/* Topics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTopics.map((topic) => (
              <div
                key={topic.id}
                className="group rounded-xl p-6 border border-[var(--text-color)]/10 cursor-pointer transition-all duration-300 hover:border-[var(--text-color)]/20 hover:shadow-lg"
                style={{ backgroundColor: cardBgColor }}
                onClick={() => handleTopicClick(topic.id)}
              >
                {/* Topic Header */}
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-[var(--text-color)] group-hover:text-blue-600 transition-colors line-clamp-2">
                    {topic.title}
                  </h3>
                  <ArrowRight className="w-4 h-4 text-[var(--text-color)]/40 group-hover:text-blue-500 transition-colors flex-shrink-0 ml-2" />
                </div>

                {/* Description */}
                <p className="text-sm text-[var(--text-color)]/70 mb-4 line-clamp-3">
                  {topic.description}
                </p>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-[var(--text-color)]/60 mb-3">
                  <div className="flex items-center space-x-1">
                    <BookOpen className="w-3 h-3" />
                    <span>{topic.sentence_count} paragraf</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{topic.estimated_reading_time} dk</span>
                  </div>
                </div>

                {/* Difficulty & Category */}
                <div className="flex items-center justify-between">
                  <span className={`text-xs font-medium ${getDifficultyColor(topic.difficulty)}`}>
                    {getDifficultyText(topic.difficulty)}
                  </span>
                  <div className="flex items-center space-x-1">
                    <div 
                      className="w-2 h-2 rounded-full" 
                      style={{ backgroundColor: topicIndex.categories[topic.category]?.color }}
                    ></div>
                    <span className="text-xs text-[var(--text-color)]/60">
                      {topicIndex.categories[topic.category]?.title}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* No Results */}
          {filteredTopics.length === 0 && (
            <div className="text-center py-12">
              <Tag className="w-12 h-12 text-[var(--text-color)]/30 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-[var(--text-color)] mb-2">
                Konu bulunamadı
              </h3>
              <p className="text-[var(--text-color)]/60">
                Arama kriterlerinizi değiştirip tekrar deneyin
              </p>
            </div>
          )}
        </div>
      </main>
    </PageLayout>
  );
};

export default TopicListPage;
