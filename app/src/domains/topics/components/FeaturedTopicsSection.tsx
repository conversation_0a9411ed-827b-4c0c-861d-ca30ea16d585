import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { BookOpen, Clock, Tag, ArrowRight, TrendingUp } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface FeaturedTopic {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimated_reading_time: number;
  sentence_count: number;
  preview: string;
  keywords: string[];
}

interface TopicCategory {
  id: string;
  title: string;
  color: string;
}

interface FeaturedTopicsData {
  categories: TopicCategory[];
  featured_topics: FeaturedTopic[];
  stats: {
    total_sentences: number;
    average_reading_time: number;
  };
}

interface FeaturedTopicsSectionProps {
  className?: string;
}

export const FeaturedTopicsSection: React.FC<FeaturedTopicsSectionProps> = ({ 
  className = '' 
}) => {
  const navigate = useNavigate();
  const [data, setData] = useState<FeaturedTopicsData | null>(null);
  const [loading, setLoading] = useState(true);

  // Auto overlay colors - tüm renkleri önceden hesapla
  const sectionBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const hoverBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const buttonBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const keywordBgColor = useAutoOverlay(4, 'var(--bg-color)');

  useEffect(() => {
    const loadFeaturedTopics = async () => {
      try {
        const response = await fetch('/data/topics/index.json');
        if (response.ok) {
          const topicData = await response.json();
          setData(topicData);
        }
      } catch (error) {
        console.error('Featured topics yüklenemedi:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedTopics();
  }, []);

  const handleTopicClick = (topicId: string) => {
    navigate(`/topics/${topicId}`);
  };

  const handleViewAllClick = () => {
    navigate('/topics');
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600';
      case 'intermediate': return 'text-yellow-600';
      case 'advanced': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'Başlangıç';
      case 'intermediate': return 'Orta';
      case 'advanced': return 'İleri';
      default: return difficulty;
    }
  };

  const getCategoryColor = (categoryId: string) => {
    const category = data?.categories.find(c => c.id === categoryId);
    return category?.color || '#6b7280';
  };

  const getCategoryTitle = (categoryId: string) => {
    const category = data?.categories.find(c => c.id === categoryId);
    return category?.title || categoryId;
  };

  if (loading) {
    return (
      <div 
        className={`rounded-xl p-6 border border-[var(--text-color)]/10 ${className}`}
        style={{ backgroundColor: sectionBgColor }}
      >
        <div className="animate-pulse">
          <div className="h-8 bg-[var(--text-color)]/10 rounded mb-6 w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-48 bg-[var(--text-color)]/10 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data || !data.featured_topics.length) {
    return null;
  }

  return (
    <div 
      className={`rounded-xl p-6 border border-[var(--text-color)]/10 ${className}`}
      style={{ backgroundColor: sectionBgColor }}
    >
      {/* Section Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[var(--text-color)]">
              Öne Çıkan Konular
            </h2>
            <p className="text-[var(--text-color)]/70">
              En popüler ve önemli konuları keşfedin
            </p>
          </div>
        </div>
        <button
          onClick={handleViewAllClick}
          className="flex items-center space-x-2 px-4 py-2 rounded-lg text-blue-600 hover:text-blue-700 transition-colors"
          style={{ backgroundColor: buttonBgColor }}
        >
          <span className="text-sm font-medium">Tümünü Gör</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>

      {/* Featured Topics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.featured_topics.map((topic) => (
          <div
            key={topic.id}
            className="group rounded-lg p-5 border border-[var(--text-color)]/10 cursor-pointer transition-all duration-300 hover:border-[var(--text-color)]/20 hover:shadow-lg hover:scale-[1.02]"
            style={{ backgroundColor: cardBgColor }}
            onClick={() => handleTopicClick(topic.id)}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = hoverBgColor;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = cardBgColor;
            }}
          >
            {/* Topic Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: getCategoryColor(topic.category) }}
                  ></div>
                  <span className="text-xs font-medium text-[var(--text-color)]/60 uppercase tracking-wide">
                    {getCategoryTitle(topic.category)}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-[var(--text-color)] group-hover:text-blue-600 transition-colors line-clamp-1">
                  {topic.title}
                </h3>
              </div>
              <ArrowRight className="w-4 h-4 text-[var(--text-color)]/40 group-hover:text-blue-500 transition-colors flex-shrink-0 ml-2" />
            </div>

            {/* Description */}
            <p className="text-sm text-[var(--text-color)]/70 mb-3 line-clamp-2">
              {topic.description}
            </p>

            {/* Preview */}
            <div className="mb-4">
              <p className="text-sm text-[var(--text-color)]/80 italic line-clamp-2">
                "{topic.preview}"
              </p>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between text-xs text-[var(--text-color)]/60 mb-3">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-1">
                  <BookOpen className="w-3 h-3" />
                  <span>{topic.sentence_count} paragraf</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{topic.estimated_reading_time} dk</span>
                </div>
              </div>
              <span className={`text-xs font-medium ${getDifficultyColor(topic.difficulty)}`}>
                {getDifficultyText(topic.difficulty)}
              </span>
            </div>

            {/* Keywords */}
            <div className="flex flex-wrap gap-1">
              {topic.keywords.slice(0, 3).map((keyword, index) => (
                <span
                  key={index}
                  className="px-2 py-1 rounded text-xs font-medium text-[var(--text-color)]/60"
                  style={{ backgroundColor: keywordBgColor }}
                >
                  {keyword}
                </span>
              ))}
              {topic.keywords.length > 3 && (
                <span className="px-2 py-1 rounded text-xs font-medium text-[var(--text-color)]/40">
                  +{topic.keywords.length - 3}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Section Footer */}
      <div className="flex items-center justify-center mt-6 pt-6 border-t border-[var(--text-color)]/10">
        <div className="flex items-center space-x-6 text-sm text-[var(--text-color)]/60">
          <div className="flex items-center space-x-1">
            <Tag className="w-4 h-4" />
            <span>{data.stats.total_sentences} toplam paragraf</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>Ortalama {data.stats.average_reading_time} dk okuma</span>
          </div>
        </div>
      </div>
    </div>
  );
};
