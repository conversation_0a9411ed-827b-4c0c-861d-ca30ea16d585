import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class AnnotationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Annotation Error Boundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <AnnotationErrorFallback onRetry={this.handleRetry} error={this.state.error} />;
    }

    return this.props.children;
  }
}

interface AnnotationErrorFallbackProps {
  onRetry: () => void;
  error?: Error;
}

function AnnotationErrorFallback({ onRetry, error }: AnnotationErrorFallbackProps) {
  const cardBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const borderColor = useAutoOverlay(8, 'var(--bg-color)');

  return (
    <div 
      className="rounded-lg border p-8 text-center"
      style={{ 
        backgroundColor: cardBgColor,
        borderColor: borderColor
      }}
    >
      <div className="text-6xl mb-4">💥</div>
      <h3 className="text-xl font-semibold mb-2 text-red-600">
        Bir şeyler ters gitti
      </h3>
      <p className="mb-4" style={{ color: 'var(--text-color)', opacity: 0.7 }}>
        Şerh sistemi beklenmedik bir hatayla karşılaştı.
      </p>
      
      {/* Error Details (Development only) */}
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mb-4 text-left">
          <summary className="cursor-pointer text-sm font-medium mb-2" style={{ color: 'var(--text-color)', opacity: 0.8 }}>
            Teknik Detaylar (Geliştirici Modu)
          </summary>
          <pre 
            className="text-xs p-3 rounded overflow-auto max-h-32"
            style={{
              backgroundColor: 'color-mix(in srgb, var(--bg-color) 96%, var(--text-color) 4%)',
              color: 'var(--text-color)',
              opacity: 0.8
            }}
          >
            {error.message}
            {error.stack && `\n\n${error.stack}`}
          </pre>
        </details>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          onClick={onRetry}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          Tekrar Dene
        </button>
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          style={{ 
            borderColor: borderColor,
            color: 'var(--text-color)'
          }}
        >
          Sayfayı Yenile
        </button>
      </div>
    </div>
  );
}

// Kompakt error fallback (Bottom Sheet için)
export function AnnotationErrorFallbackCompact({ onRetry }: Omit<AnnotationErrorFallbackProps, 'error'>) {
  return (
    <div className="p-6 text-center">
      <div className="text-4xl mb-3">😵</div>
      <h3 className="text-lg font-medium mb-2 text-red-600">
        Hata oluştu
      </h3>
      <p className="text-sm mb-4 text-gray-600">
        Şerhler yüklenirken bir sorun yaşandı.
      </p>
      
      <div className="flex gap-2 justify-center">
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
        >
          Tekrar Dene
        </button>
      </div>
    </div>
  );
}

// HOC wrapper for easier usage
export function withAnnotationErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <AnnotationErrorBoundary fallback={fallback}>
        <Component {...props} />
      </AnnotationErrorBoundary>
    );
  };
}
