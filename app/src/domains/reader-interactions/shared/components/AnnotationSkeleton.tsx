import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface AnnotationSkeletonProps {
  count?: number;
}

export function AnnotationSkeleton({ count = 3 }: AnnotationSkeletonProps) {
  const cardBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const borderColor = useAutoOverlay(8, 'var(--bg-color)');
  const shimmerColor = useAutoOverlay(6, 'var(--bg-color)');
  const selectedTextBgColor = useAutoOverlay(4, 'var(--bg-color)');

  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="rounded-lg border p-4 animate-pulse"
          style={{ 
            backgroundColor: cardBgColor,
            borderColor: borderColor
          }}
        >
          {/* Header Skeleton */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              {/* Icon Skeleton */}
              <div 
                className="w-6 h-6 rounded"
                style={{ backgroundColor: shimmerColor }}
              />
              <div>
                {/* Title Skeleton */}
                <div 
                  className="h-4 rounded w-20 mb-1"
                  style={{ backgroundColor: shimmerColor }}
                />
                {/* Date Skeleton */}
                <div 
                  className="h-3 rounded w-16"
                  style={{ backgroundColor: shimmerColor }}
                />
              </div>
            </div>
            {/* Arrow Skeleton */}
            <div 
              className="w-4 h-4 rounded"
              style={{ backgroundColor: shimmerColor }}
            />
          </div>

          {/* Selected Text Skeleton */}
          <div className="mb-3">
            <div 
              className="p-3 rounded border-l-4 border-blue-400"
              style={{ backgroundColor: selectedTextBgColor }}
            >
              <div 
                className="h-3 rounded w-3/4 mb-1"
                style={{ backgroundColor: shimmerColor }}
              />
              <div 
                className="h-3 rounded w-1/2"
                style={{ backgroundColor: shimmerColor }}
              />
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="space-y-2">
            <div 
              className="h-3 rounded w-full"
              style={{ backgroundColor: shimmerColor }}
            />
            <div 
              className="h-3 rounded w-5/6"
              style={{ backgroundColor: shimmerColor }}
            />
            <div 
              className="h-3 rounded w-2/3"
              style={{ backgroundColor: shimmerColor }}
            />
          </div>
        </div>
      ))}
    </div>
  );
}

// Bottom Sheet için kompakt skeleton
export function AnnotationSkeletonCompact({ count = 3 }: AnnotationSkeletonProps) {
  const shimmerColor = useAutoOverlay(6, 'var(--bg-color)');

  return (
    <div className="divide-y divide-gray-100">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="p-4 animate-pulse">
          {/* Header */}
          <div className="flex items-center space-x-3 mb-3">
            <div 
              className="w-6 h-6 rounded"
              style={{ backgroundColor: shimmerColor }}
            />
            <div>
              <div 
                className="h-3 rounded w-16 mb-1"
                style={{ backgroundColor: shimmerColor }}
              />
              <div 
                className="h-2 rounded w-12"
                style={{ backgroundColor: shimmerColor }}
              />
            </div>
          </div>

          {/* Selected Text */}
          <div className="mb-3">
            <div 
              className="p-3 rounded"
              style={{ backgroundColor: selectedTextBgColor }}
            >
              <div 
                className="h-3 rounded w-4/5"
                style={{ backgroundColor: shimmerColor }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="space-y-1">
            <div 
              className="h-3 rounded w-full"
              style={{ backgroundColor: shimmerColor }}
            />
            <div 
              className="h-3 rounded w-3/4"
              style={{ backgroundColor: shimmerColor }}
            />
          </div>
        </div>
      ))}
    </div>
  );
}
