/**
 * Shared Types for Reader Interactions
 * Common types used across all sub-domains
 */

// Base annotation types
export type AnnotationType = 'note' | 'sherh' | 'highlight' | 'bookmark';

// Text selection interface
export interface TextSelection {
  start: number;
  end: number;
  text: string;
  sentence_id: string | string[];
}

// Position recovery interface
export interface AnnotationPosition {
  start: number;
  end: number;
  confidence: number; // 0-1 arası güven skoru
}

// Error handling
export interface AnnotationError {
  code: string;
  message: string;
  details?: unknown;
}

// Service response wrapper
export interface AnnotationServiceResponse<T> {
  data: T | null;
  error: AnnotationError | null;
}

// Common metadata structure
export interface BaseMetadata {
  created_at: string;
  updated_at: string;
  user_id: string;
  book_id: string;
  section_id: string;
  sentence_id: string | string[];
}

// Position data structure
export interface PositionData {
  selection_start: number;
  selection_end: number;
  selected_text: string;
  prefix_text: string;
  suffix_text: string;
  word_proximity: string[];
  text_hash: string;
  sentence_hash: string;
}

// Visual styling
export interface VisualStyle {
  color: string;
  highlight_style?: 'background' | 'text';
  opacity?: number;
}

// Social features
export interface SocialFeatures {
  is_public: boolean;
  tags: string[];
  category?: string;
}

// Re-export all types from the main types file
export * from './types';
