import { useState } from 'react';
import { supabase } from '@shared/utils/supabaseClient';
import type { Annotation } from '../types';

export const useAnnotationManager = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteAnnotation = async (annotationId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const { error: deleteError } = await supabase
        .from('text_annotations')
        .delete()
        .eq('id', annotationId);

      if (deleteError) {
        throw deleteError;
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Annotation silinirken hata oluştu';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const createAnnotation = async (annotation: Partial<Annotation>) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error: createError } = await supabase
        .from('text_annotations')
        .insert([annotation])
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Annotation oluşturulurken hata oluştu';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateAnnotation = async (annotationId: string, updates: Partial<Annotation>) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error: updateError } = await supabase
        .from('text_annotations')
        .update(updates)
        .eq('id', annotationId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Annotation güncellenirken hata oluştu';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    deleteAnnotation,
    createAnnotation,
    updateAnnotation
  };
};
