import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '@domains/auth/store/authStore';
import { collectionService } from '../services/collectionService';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type {
  BookmarkCollection,
  CreateCollectionInput,
  UpdateCollectionInput
} from '../../shared/types';

/**
 * useCollectionManager Hook
 * Bookmark koleksiyonları için CRUD işlemleri ve state yönetimi
 */
export function useCollectionManager() {
  const { user } = useAuthStore();
  const [collections, setCollections] = useState<BookmarkCollection[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Koleksiyonları yükler
   */
  const loadCollections = useCallback(async () => {
    if (!user) {
      setCollections([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await collectionService.getUserCollections(user.id);

      if (response.error) {
        setError(response.error.message);
        return;
      }

      setCollections(response.data || []);
    } catch (err) {
      console.error('[useCollectionManager] Load error:', err);
      setError('Koleksiyonlar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Yeni koleksiyon oluşturur
   */
  const createCollection = useCallback(async (input: Omit<CreateCollectionInput, 'user_id'>): Promise<BookmarkCollection | null> => {
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await collectionService.createCollection({
        ...input,
        user_id: user.id
      });

      if (response.error) {
        setError(response.error.message);
        return null;
      }

      const newCollection = response.data!;
      setCollections(prev => [newCollection, ...prev]);

      return newCollection;
    } catch (err) {
      console.error('[useCollectionManager] Create error:', err);
      setError('Koleksiyon oluşturulurken hata oluştu');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Koleksiyonu günceller
   */
  const updateCollection = useCallback(async (
    id: string,
    input: UpdateCollectionInput
  ): Promise<BookmarkCollection | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await collectionService.updateCollection(id, input);

      if (response.error) {
        setError(response.error.message);
        return null;
      }

      const updatedCollection = response.data!;
      setCollections(prev =>
        prev.map(collection =>
          collection.id === id ? updatedCollection : collection
        )
      );

      return updatedCollection;
    } catch (err) {
      console.error('[useCollectionManager] Update error:', err);
      setError('Koleksiyon güncellenirken hata oluştu');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Koleksiyonu siler
   */
  const deleteCollection = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await collectionService.deleteCollection(id);

      if (response.error) {
        setError(response.error.message);
        return false;
      }

      setCollections(prev => prev.filter(collection => collection.id !== id));
      return true;
    } catch (err) {
      console.error('[useCollectionManager] Delete error:', err);
      setError('Koleksiyon silinirken hata oluştu');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Varsayılan koleksiyonu getirir
   */
  const getDefaultCollection = useCallback((): BookmarkCollection | null => {
    return collections.find(collection => collection.is_default) || null;
  }, [collections]);

  /**
   * Koleksiyonu ID ile bulur
   */
  const findCollectionById = useCallback((id: string): BookmarkCollection | undefined => {
    return collections.find(collection => collection.id === id);
  }, [collections]);

  /**
   * Varsayılan koleksiyonları oluşturur
   */
  const createDefaultCollections = useCallback(async (): Promise<boolean> => {
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await collectionService.createDefaultCollections(user.id);

      if (response.error) {
        setError(response.error.message);
        return false;
      }

      setCollections(response.data || []);
      return true;
    } catch (err) {
      console.error('[useCollectionManager] Create defaults error:', err);
      setError('Varsayılan koleksiyonlar oluşturulurken hata oluştu');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Error'ı temizler
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Koleksiyonları temizler
   */
  const clearCollections = useCallback(() => {
    setCollections([]);
    setError(null);
  }, []);

  // İlk yükleme
  useEffect(() => {
    if (user) {
      loadCollections();
    } else {
      clearCollections();
    }
  }, [user, loadCollections, clearCollections]);

  return {
    // State
    collections,
    loading,
    error,

    // Actions
    loadCollections,
    createCollection,
    updateCollection,
    deleteCollection,
    createDefaultCollections,
    clearError,
    clearCollections,

    // Helpers
    getDefaultCollection,
    findCollectionById,

    // Computed
    totalCount: collections.length,
    hasCollections: collections.length > 0,
    defaultCollection: getDefaultCollection()
  };
}
