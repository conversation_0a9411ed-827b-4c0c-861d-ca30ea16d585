import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { X, Check, Edit3, Type, Sparkles } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface NoteBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  initialContent?: string;
  noteContent?: string;
  onNoteContentChange?: React.Dispatch<React.SetStateAction<string>>;
  onSave: (content: string) => void | Promise<void>;
  loading?: boolean;
}

export const NoteBottomSheet: React.FC<NoteBottomSheetProps> = ({
  isOpen,
  onClose,
  selectedText,
  initialContent = '',
  noteContent: externalNoteContent,
  onNoteContentChange: _onNoteContentChange,
  onSave,
  loading = false
}) => {
  const [noteContent, setNoteContent] = useState(externalNoteContent || initialContent);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Geliştirilmiş, daha opak renk paleti
  const sheetBg = 'var(--bg-color)';
  const borderColor = useAutoOverlay(12, sheetBg);
  const cardBg = useAutoOverlay(4, sheetBg);
  const successColor = '#10b981';

  // Auto-focus textarea when sheet opens
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      setTimeout(() => textareaRef.current?.focus(), 200);
    }
  }, [isOpen]);

  // Reset states when sheet closes
  useEffect(() => {
    if (!isOpen) {
      setIsSaving(false);
      setShowSuccess(false);
      // Reset content only if it's not in edit mode
      if (initialContent === '') {
        setNoteContent('');
      }
    } else {
      // If opening, set the content to initialContent (for editing)
      setNoteContent(initialContent);
    }
  }, [isOpen, initialContent]);

  // Handle save with premium animation
  const handleSave = async () => {
    if (!noteContent.trim()) return;
    
    setIsSaving(true);
    try {
      await onSave(noteContent);
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 1600);
    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  const isEditing = !!initialContent;
  
  const sheetPortal = document.getElementById('sheet-portal');
  if (!sheetPortal) return null; // Portal root not found

  return ReactDOM.createPortal(
    <>
      {/* Premium Backdrop */}
      <div
        className="fixed inset-0 bg-black/30 backdrop-blur-md z-60 transition-all duration-300"
        onClick={onClose}
      />

      {/* Premium Sheet */}
      <div
        className="fixed z-60 bottom-0 left-0 right-0 md:bottom-auto md:top-1/2 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 md:w-[520px] md:max-h-[90vh] md:rounded-3xl rounded-t-3xl shadow-2xl border-t md:border flex flex-col max-h-[92vh] h-[85vh] md:h-[700px]"
        style={{
          backgroundColor: sheetBg,
          borderColor: borderColor,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.2)'
        }}
      >
        {/* Premium Header */}
        <div className="flex items-center gap-3 px-5 py-2 border-b" style={{ borderColor: borderColor }}>
          <div 
            className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden"
            style={{ backgroundColor: 'var(--text-color)/10' }}
          >
            <Edit3 size={16} style={{ color: 'var(--text-color)' }} />
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              {isEditing ? 'Notu Düzenle' : 'Yeni Not'}
            </h2>
            <p className="text-xs opacity-50 truncate" style={{ color: 'var(--text-color)' }}>
              {selectedText}
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-200 group"
            style={{ color: 'var(--text-color)' }}
          >
            <X size={14} className="group-hover:scale-110 transition-transform" />
          </button>
        </div>

        {/* Premium Content */}
        <div className="flex-1 flex flex-col p-6 min-h-0">
          {/* Premium Writing Zone */}
          <div 
            className="flex-1 rounded-2xl p-6 border-2 border-dashed transition-all duration-300 relative min-h-0 group"
            style={{
              backgroundColor: cardBg,
              borderColor: noteContent.trim() ? 'var(--text-color)' : borderColor
            }}
          >
            {/* Floating Premium Hint */}
            {!noteContent.trim() && (
              <div className="absolute inset-0 flex items-center justify-center opacity-30 pointer-events-none">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 rounded-full" style={{ backgroundColor: 'var(--text-color)/10' }}>
                    <Type size={24} style={{ color: 'var(--text-color)' }} />
                  </div>
                  <p className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
                    Fikirlerinizi yazın...
                  </p>
                </div>
              </div>
            )}
            
            <textarea
              ref={textareaRef}
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder=""
              className="w-full h-full bg-transparent text-base leading-relaxed resize-none focus:outline-none relative z-10"
              style={{ color: 'var(--text-color)' }}
              disabled={isSaving || loading}
            />
          </div>

          {/* Premium Bottom Bar */}
          <div className="flex items-center justify-between mt-6 pt-4 border-t" style={{ borderColor: borderColor }}>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="text-xs font-medium opacity-40" style={{ color: 'var(--text-color)' }}>
                  {noteContent.length}
                </div>
                <div className="text-xs opacity-40" style={{ color: 'var(--text-color)' }}>
                  karakter
                </div>
              </div>
              {noteContent.trim() && (
                <div className="flex items-center gap-1 text-xs px-2 py-1 rounded-full" style={{ backgroundColor: 'var(--text-color)/15', color: 'var(--text-color)' }}>
                  <Sparkles size={12} />
                  Hazır
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:bg-gray-50"
                style={{ color: 'var(--text-color)' }}
              >
                İptal
              </button>
              
              <button
                onClick={handleSave}
                disabled={!noteContent.trim() || isSaving || loading}
                className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[130px] justify-center shadow-lg hover:shadow-xl relative overflow-hidden"
                style={{
                  backgroundColor: showSuccess ? successColor : 'var(--text-color)',
                  color: 'var(--bg-color)',
                  transform: showSuccess ? 'scale(1.05)' : 'scale(1)',
                  boxShadow: showSuccess 
                    ? `0 10px 30px ${successColor}30`
                    : `0 6px 20px color-mix(in srgb, var(--text-color) 25%, transparent)`
                }}
              >
                {showSuccess && (
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent animate-pulse" />
                )}
                {showSuccess ? (
                  <>
                    <Check size={16} />
                    Kaydedildi!
                  </>
                ) : isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span className="ml-1">Kaydediliyor</span>
                  </>
                ) : (
                  <>
                    <Check size={16} />
                    {isEditing ? 'Güncelle' : 'Kaydet'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,
    sheetPortal
  );
};
