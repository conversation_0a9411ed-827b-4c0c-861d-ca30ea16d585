import React, { useState, useEffect } from 'react';
import { X, Plus, Edit3, Trash2, Calendar, FileText, AlertTriangle } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useAuthStore } from '@domains/auth/store/authStore';
import { AnnotationService } from '@domains/reader-interactions/services/AnnotationService';
import { useAnnotationManager } from '@domains/reader-interactions/annotations/hooks/useAnnotationManager';

interface NoteItem {
  id: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
}

interface NotesListSheetProps {
  isOpen: boolean;
  onClose: () => void;
  verseKey: string;
  surahName?: string;
  onAddNote: () => void;
  onEditNote: (note: NoteItem) => void; 
}

export const NotesListSheet: React.FC<NotesListSheetProps> = ({
  isOpen,
  onClose,
  verseKey,
  surahName,
  onAddNote,
  onEditNote
}) => {
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);

  const sheetBg = 'var(--bg-color)';
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');
  const cardBg = useAutoOverlay(5, 'var(--bg-color)');
  const hoverBg = useAutoOverlay(8, 'var(--bg-color)');

  const { user } = useAuthStore();
  const { deleteAnnotation, loading: isProcessing } = useAnnotationManager();

  useEffect(() => {
    if (isOpen && verseKey) {
      loadNotes();
    }
  }, [isOpen, verseKey]);

  const loadNotes = async () => {
    setLoading(true);
    try {
      if (!user) {
        setNotes([]);
        return;
      }
      const notesData = await AnnotationService.getNotes(user.id, verseKey);
      setNotes(notesData);
    } catch (error) {
      console.error('❌ NotesListSheet - Notlar yüklenirken hata:', error);
      setNotes([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    const success = await deleteAnnotation(noteId);
    if (success) {
      setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
      setNoteToDelete(null); 
    } else {
      setNoteToDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  const [surahNum, verseNum] = verseKey ? verseKey.split('-') : [];
  const selectedText = verseNum 
    ? `${surahName || `Sûre ${surahNum}`}, ${verseNum}. Ayet`
    : 'Ayet bilgisi yükleniyor...';

  return (
    <>
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 transition-opacity duration-300"
        onClick={onClose}
      />

      {noteToDelete && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
          <div 
            className="w-full max-w-sm rounded-2xl p-6 shadow-2xl border"
            style={{ backgroundColor: sheetBg, borderColor }}
          >
            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-500" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-[var(--text-color)]">Notu Sil</h3>
              <p className="text-sm text-[var(--text-color)]/70 mb-6">
                Bu notu kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
              </p>
              <div className="w-full flex gap-3">
                <button
                  onClick={() => setNoteToDelete(null)}
                  className="flex-1 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors"
                  style={{ backgroundColor: hoverBg }}
                  disabled={isProcessing}
                >
                  İptal
                </button>
                <button
                  onClick={() => handleDeleteNote(noteToDelete)}
                  className="flex-1 px-4 py-2.5 rounded-lg text-sm font-medium bg-red-600 text-white hover:bg-red-700 transition-colors flex items-center justify-center"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <div className="w-4 h-4 border-2 border-white/50 border-t-white rounded-full animate-spin" />
                  ) : (
                    'Sil'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 md:bottom-auto md:top-1/2 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 md:w-[520px] md:max-h-[90vh] md:rounded-3xl rounded-t-3xl shadow-2xl border-t md:border flex flex-col max-h-[92vh] h-[80vh] md:h-[650px]"
        style={{
          backgroundColor: sheetBg,
          borderColor: borderColor,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Header */}
        <div className="flex items-center gap-3 px-5 py-2 border-b" style={{ borderColor }}>
          <div 
            className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden"
            style={{ backgroundColor: 'var(--text-color)/10' }}
          >
            <FileText size={16} style={{ color: 'var(--text-color)' }} />
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              Ayet Notları
            </h2>
            <p className="text-xs opacity-50 truncate" style={{ color: 'var(--text-color)' }}>
              {selectedText}
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-red-500/10 text-[var(--text-color)] hover:text-red-500 transition-all duration-200 group"
          >
            <X size={14} className="group-hover:scale-110 transition-transform" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col p-4 pt-4 md:p-6 min-h-0">
          <div className="flex-1 rounded-xl p-2 md:p-3 relative overflow-y-auto">
            {loading ? (
              <div className="flex flex-col items-center justify-center h-full text-sm opacity-70 text-[var(--text-color)]">
                <div className="w-8 h-8 border-2 border-[var(--text-color)]/20 border-t-[var(--text-color)] rounded-full animate-spin mb-4"></div>
                <p className="text-sm">Notlar Yükleniyor...</p>
              </div>
            ) : notes.length > 0 ? (
              <div className="space-y-3">
                <div className="px-2 mb-3">
                  <h4 className="text-xs font-semibold uppercase opacity-50 tracking-wider text-[var(--text-color)]">
                    {notes.length} Not
                  </h4>
                </div>
                {notes.map((note) => (
                  <div
                    key={note.id}
                    className="group p-4 rounded-xl transition-all duration-200 hover:bg-[--hover-bg]"
                    style={{ 
                      backgroundColor: cardBg,
                      '--hover-bg': hoverBg
                    } as React.CSSProperties}
                  >
                    <div className="flex justify-between items-start gap-3">
                      <div className="flex-1">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap" style={{ color: 'var(--text-color)' }}>
                          {note.content}
                        </p>
                      </div>
                      <div className="flex space-x-0.5 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => onEditNote(note)}
                          className="p-2 rounded-full text-[var(--text-color)]/60 hover:text-blue-500 hover:bg-blue-500/10 transition-all duration-200"
                        >
                          <Edit3 size={14} />
                        </button>
                        <button
                          onClick={() => setNoteToDelete(note.id)}
                          className="p-2 rounded-full text-[var(--text-color)]/60 hover:text-red-500 hover:bg-red-500/10 transition-all duration-200"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center text-xs opacity-50 mt-3" style={{ color: 'var(--text-color)' }}>
                      <Calendar size={12} className="mr-1.5" />
                      {formatDate(note.createdAt)}
                      {note.updatedAt && note.updatedAt !== note.createdAt && (
                        <span className="ml-2 opacity-80">(düzenlendi)</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <div className="w-16 h-16 rounded-full bg-gray-500/5 flex items-center justify-center mb-4">
                  <FileText size={28} className="text-[var(--text-color)]/30" />
                </div>
                <p className="text-base font-medium mb-1 text-[var(--text-color)]">Henüz Not Eklenmemiş</p>
                <p className="text-sm opacity-60 text-[var(--text-color)]">
                  Bu ayetle ilgili ilk notu siz ekleyin.
                </p>
              </div>
            )}
          </div>
          {/* Footer - Add Note Button */}
          <div className="pt-4 mt-auto border-t" style={{ borderColor }}>
            <button
              onClick={onAddNote}
              className="w-full px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
              style={{
                backgroundColor: 'var(--text-color)',
                color: 'var(--bg-color)',
                boxShadow: `0 6px 20px color-mix(in srgb, var(--text-color) 25%, transparent)`
              }}
            >
              <Plus size={18} />
              <span>Yeni Not Ekle</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
