import { useMemo } from 'react';
import parse from 'html-react-parser';

interface AnnotationHighlighterProps {
  bookId?: string;
  sectionId?: string;
  sentenceId: string;
  text: string;
  isEnabled?: boolean;
  onAnnotationClick?: (annotation: any) => void;
  annotations?: any[]; // Annotation'ları prop olarak al
  showNotes?: boolean;
  showHighlights?: boolean;
  showBookmarks?: boolean;
}

/**
 * @deprecated Legacy Annotation Highlighter - Use HighlightRenderer instead
 *
 * ⚠️ Bu component deprecated! Yeni projeler için HighlightRenderer kullanın:
 *
 * ```tsx
 * // ❌ ESKİ YÖNTEM
 * import { AnnotationHighlighter } from '@domains/reader-interactions/highlights';
 *
 * // ✅ YENİ YÖNTEM
 * import { HighlightRenderer } from '@domains/reader-interactions/highlights';
 *
 * <HighlightRenderer
 *   text={text}
 *   annotations={annotations}
 *   onAnnotationClick={handleClick}
 * />
 * ```
 *
 * Basit Annotation Highlighter
 * Annotation'ları prop olarak alır - store bağımlılığı yok
 */
export function AnnotationHighlighter({
  sentenceId,
  text,
  isEnabled = true,
  onAnnotationClick,
  annotations = [],
  showNotes = true,
  showHighlights = true,
  showBookmarks = true
}: AnnotationHighlighterProps) {
  // Bu sentence için annotation var mı kontrol et (visibility state'lerine göre filtrele)
  const sentenceAnnotations = useMemo(() => {
    if (!annotations || annotations.length === 0) {
      return [];
    }
    // Sentence ID'si eşleşen annotation'ları filtrele ve visibility state'lerine göre göster
    return annotations.filter(annotation => {
      const annotationSentenceIds = Array.isArray(annotation.sentence_id)
        ? annotation.sentence_id
        : [annotation.sentence_id];
      const isMatchingSentence = annotationSentenceIds.includes(sentenceId);

      if (!isMatchingSentence) return false;

      // Visibility state'lerine göre filtrele
      switch (annotation.annotation_type) {
        case 'note':
          return showNotes;
        case 'highlight':
          return showHighlights;
        case 'bookmark':
          return showBookmarks;
        default:
          return true;
      }
    });
  }, [annotations, sentenceId, showNotes, showHighlights, showBookmarks]);

  // HTML'den düz text çıkar (pozisyon hesaplama için)
  // const getPlainText = (htmlText: string): string => {
  //   const tempDiv = document.createElement('div');
  //   tempDiv.innerHTML = htmlText;
  //   return tempDiv.textContent || tempDiv.innerText || '';
  // };

  // Düz text versiyonu (sadece pozisyon hesaplama için)
  // const plainText = getPlainText(text); // Şimdilik kullanılmıyor

  // HTML'den düz text çıkar (pozisyon karşılaştırması için)
  const getPlainText = (htmlText: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlText;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  const plainText = getPlainText(text);

  // Eğer highlighting kapalıysa veya annotation yoksa normal render
  if (!isEnabled || sentenceAnnotations.length === 0) {
    return <>{parse(text)}</>;
  }

  // Basit ve etkili segmentasyon algoritması
  const createAnnotatedHTML = () => {
    if (sentenceAnnotations.length === 0) {
      return [{ content: parse(text), isAnnotated: false, annotation: null }];
    }

    // Tüm annotation pozisyonlarını topla
    const positions = new Set<number>();
    positions.add(0);
    positions.add(plainText.length);

    // Her annotation'ın başlangıç ve bitiş pozisyonlarını ekle
    for (const annotation of sentenceAnnotations) {
      if (annotation.selection_start >= 0 && annotation.selection_end <= plainText.length) {
        positions.add(annotation.selection_start);
        positions.add(annotation.selection_end);
      }
    }

    // Pozisyonları sırala
    const sortedPositions = Array.from(positions).sort((a, b) => a - b);

    console.log('[AnnotationHighlighter] Pozisyonlar:', sortedPositions);

    // Her segment için annotation'ları belirle
    const segments = [];
    for (let i = 0; i < sortedPositions.length - 1; i++) {
      const segmentStart = sortedPositions[i];
      const segmentEnd = sortedPositions[i + 1];

      if (segmentStart >= segmentEnd) continue;

      const segmentPlainText = plainText.substring(segmentStart, segmentEnd);
      if (!segmentPlainText.trim()) continue;

      // Bu segment için aktif annotation'ları bul
      const activeAnnotations = sentenceAnnotations.filter(annotation => {
        return annotation.selection_start <= segmentStart &&
               annotation.selection_end >= segmentEnd;
      });



      if (activeAnnotations.length === 0) {
        // Annotation'sız segment - HTML'i koru
        const segmentHtml = convertPlainToHtml(segmentPlainText, segmentStart);
        segments.push({
          content: segmentHtml,
          isAnnotated: false,
          annotation: null
        });
      } else {
        // Annotation'lı segment - çoklu stil desteği
        const backgroundAnnotations = activeAnnotations.filter(a =>
          a.annotation_type === 'highlight' &&
          (a.highlight_style === 'background' || !a.highlight_style)
        );
        const textAnnotations = activeAnnotations.filter(a =>
          a.annotation_type === 'highlight' &&
          a.highlight_style === 'text'
        );
        const noteAnnotations = activeAnnotations.filter(a =>
          a.annotation_type === 'note'
        );
        const bookmarkAnnotations = activeAnnotations.filter(a =>
          a.annotation_type === 'bookmark'
        );



        // En üstteki annotation'ları seç (z-index mantığı)
        // Daha küçük pozisyon aralığına sahip annotation üsttedir
        const getTopAnnotation = (annotations: any[]) => {
          if (annotations.length === 0) return null;

          return annotations.reduce((top, current) => {
            const topRange = top.selection_end - top.selection_start;
            const currentRange = current.selection_end - current.selection_start;

            // Daha küçük aralık = daha spesifik = üstte
            if (currentRange < topRange) {
              return current;
            } else if (currentRange === topRange) {
              // Aynı aralıksa, en son eklenen
              return new Date(current.created_at) > new Date(top.created_at) ? current : top;
            }
            return top;
          });
        };

        const latestBackground = getTopAnnotation(backgroundAnnotations);
        const latestText = getTopAnnotation(textAnnotations);
        const latestNote = getTopAnnotation(noteAnnotations);
        const latestBookmark = getTopAnnotation(bookmarkAnnotations);

        // Öncelik sırası: note → bookmark → highlight (background/text)
        const primaryAnnotation = latestNote || latestBookmark || latestBackground || latestText;



        // HTML'i koru
        const segmentHtml = convertPlainToHtml(segmentPlainText, segmentStart);
        segments.push({
          content: segmentHtml,
          isAnnotated: true,
          annotation: primaryAnnotation,
          multipleStyles: {
            background: latestBackground,
            text: latestText,
            note: latestNote,
            bookmark: latestBookmark
          }
        });
      }
    }

    return segments;
  };

  // Plain text'i HTML'e çevir (pozisyona göre)
  const convertPlainToHtml = (plainSegment: string, startPos: number) => {
    // Basit yaklaşım: HTML etiketlerini korumak için orijinal text'ten çıkar
    let htmlResult = '';
    let htmlIndex = 0;
    let plainIndex = 0;
    let targetPlainIndex = 0;

    // Başlangıç pozisyonuna kadar git
    while (htmlIndex < text.length && plainIndex < startPos) {
      const char = text[htmlIndex];

      if (char === '<') {
        // HTML tag'i atla
        const tagEnd = text.indexOf('>', htmlIndex);
        if (tagEnd !== -1) {
          htmlIndex = tagEnd + 1;
        } else {
          htmlIndex++;
        }
      } else {
        plainIndex++;
        htmlIndex++;
      }
    }

    // Segment'i çıkar
    while (htmlIndex < text.length && targetPlainIndex < plainSegment.length) {
      const char = text[htmlIndex];

      if (char === '<') {
        // HTML tag'i ekle
        const tagEnd = text.indexOf('>', htmlIndex);
        if (tagEnd !== -1) {
          htmlResult += text.substring(htmlIndex, tagEnd + 1);
          htmlIndex = tagEnd + 1;
        } else {
          htmlResult += char;
          htmlIndex++;
        }
      } else {
        // Normal karakter
        if (char === plainSegment[targetPlainIndex]) {
          htmlResult += char;
          targetPlainIndex++;
        }
        htmlIndex++;
      }
    }

    return htmlResult || plainSegment; // Fallback olarak plain text döndür
  };

  const segments = createAnnotatedHTML();

  return (
    <>
      {segments.map((segment, index) => {
        if (!segment.isAnnotated) {
          // Normal text content - HTML formatting'i koru (bold, italic, vb.)
          return (
            <span
              key={index}
              dangerouslySetInnerHTML={{ __html: segment.content }}
            />
          );
        }

        // Annotation'lı HTML content
        const annotation = segment.annotation;
        const multipleStyles = (segment as any).multipleStyles;

        const getAnnotationStyle = () => {
          // Çoklu stil desteği - kayma önleyici base stiller
          const style: any = {
            display: 'inline',
            margin: '0',
            padding: '0',
            border: 'none',
            outline: 'none',
            boxSizing: 'border-box',
            verticalAlign: 'baseline',
            lineHeight: 'inherit',
            fontSize: 'inherit',
            fontWeight: 'inherit',
            fontFamily: 'inherit'
          };

          // Box-shadow'ları birleştirmek için array
          const shadows: string[] = [];

          console.log('[AnnotationHighlighter] Stil hesaplama:', {
            annotation: annotation?.annotation_type,
            multipleStyles: multipleStyles,
            hasBackground: !!multipleStyles?.background,
            hasText: !!multipleStyles?.text,
            hasNote: !!multipleStyles?.note,
            hasBookmark: !!multipleStyles?.bookmark
          });

          // Background annotation varsa
          if (multipleStyles?.background) {
            const bgColor = multipleStyles.background.color || '#fbbf24';
            // Karanlık modda daha belirgin olması için opacity orta seviyede
            style.backgroundColor = `${bgColor}60`;
            console.log('[AnnotationHighlighter] Background uygulandı:', bgColor);
          }

          // Text annotation varsa
          if (multipleStyles?.text) {
            const textColor = multipleStyles.text.color || '#fbbf24';
            style.color = textColor;
            console.log('[AnnotationHighlighter] Text color uygulandı:', textColor);
          }

          // Note annotation varsa - sarı alt çizgi ve arka plan ekle
          if (multipleStyles?.note) {
            shadows.push('inset 0 -2px 0 0 #fbbf24');
            style.backgroundColor = style.backgroundColor || '#fbbf2430'; // Sarı arka plan
            console.log('[AnnotationHighlighter] Note alt çizgisi ve arka plan eklendi');
          }

          // Bookmark annotation varsa - yeşil alt çizgi ve arka plan ekle
          if (multipleStyles?.bookmark) {
            shadows.push('inset 0 -2px 0 0 #10b981');
            style.backgroundColor = style.backgroundColor || '#10b98130'; // Yeşil arka plan
            console.log('[AnnotationHighlighter] Bookmark alt çizgisi ve arka plan eklendi');
          }

          // Fallback: Tek annotation varsa eski mantık
          if (!multipleStyles && annotation) {
            console.log('[AnnotationHighlighter] Fallback tek annotation:', annotation.annotation_type);
            switch (annotation.annotation_type) {
              case 'highlight': {
                const highlightStyle = annotation.highlight_style || 'background';
                const color = annotation.color || '#fbbf24';

                if (highlightStyle === 'text') {
                  style.color = color;
                } else {
                  // Karanlık modda daha belirgin olması için opacity orta seviyede
                  style.backgroundColor = `${color}60`;
                }
                break;
              }
              case 'note': {
                // Şerhler için sarı alt çizgi ve arka plan
                shadows.push('inset 0 -2px 0 0 #fbbf24');
                style.backgroundColor = '#fbbf2430';
                break;
              }
              case 'bookmark': {
                // Kaydedilenler için yeşil alt çizgi ve arka plan
                shadows.push('inset 0 -2px 0 0 #10b981');
                style.backgroundColor = '#10b98130';
                break;
              }
            }
          }

          // Box-shadow'ları birleştir
          if (shadows.length > 0) {
            style.boxShadow = shadows.join(', ');
          }

          console.log('[AnnotationHighlighter] Final stil:', style);
          return style;
        };

        // Annotation ikonunu ekle (sadece note ve bookmark için)
        const getAnnotationIcon = () => {
          switch (annotation?.annotation_type) {
            case 'note':
              return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#fbbf24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>`;
            case 'bookmark':
              return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/></svg>`;
            case 'highlight':
              return ''; // Normal highlight'larda ikon yok
            default: return '';
          }
        };

        const icon = getAnnotationIcon();
        const contentWithIcon = icon ?
          segment.content + `<span class="annotation-icon" style="margin-left: 3px; vertical-align: baseline; display: inline-block;">${icon}</span>` :
          segment.content;

        return (
          <span
            key={index}
            className="annotation-highlighted"
            style={{
              ...getAnnotationStyle(),
              cursor: 'pointer',
              // Kayma önleyici stiller - padding/margin/border kaldırıldı
              padding: '0',
              margin: '0',
              border: 'none',
              outline: 'none',
              display: 'inline',
              verticalAlign: 'baseline',
              lineHeight: 'inherit',
              fontSize: 'inherit',
              fontWeight: 'inherit',
              fontFamily: 'inherit',
              transition: 'background-color 0.2s ease, color 0.2s ease'
            }}
            title={`${annotation?.annotation_type === 'note' ? 'Şerh' : 'Vurgulama'}: ${
                     annotation?.annotation_content || 'İçerik yok'}`}
            onClick={() => {
              console.log('Annotation clicked:', annotation);
              if (onAnnotationClick) {
                onAnnotationClick(annotation);
              }
            }}
            dangerouslySetInnerHTML={{ __html: contentWithIcon }}
          />
        );
      })}
    </>
  );
}

// CSS stilleri için yardımcı component
export function AnnotationHighlighterStyles() {
  return (
    <style dangerouslySetInnerHTML={{
      __html: `
        .annotation-highlighted {
          cursor: pointer;
          transition: background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
          /* Kayma önleyici stiller */
          display: inline;
          margin: 0;
          padding: 0;
          border: none;
          outline: none;
          box-sizing: border-box;
          vertical-align: baseline;
          line-height: inherit;
          font-size: inherit;
          font-weight: inherit;
          font-family: inherit;
        }

        .annotation-note {
          /* Şerhler için sarı alt çizgi ve arka plan */
          box-shadow: inset 0 -2px 0 0 #fbbf24;
          background-color: #fbbf2430;
        }

        .annotation-bookmark {
          /* Kaydedilenler için yeşil alt çizgi ve arka plan */
          box-shadow: inset 0 -2px 0 0 #10b981;
          background-color: #10b98130;
        }

        .annotation-highlight {
          background-color: rgba(251, 191, 36, 0.4);
          /* Sadece arka plan, alt çizgi yok */
        }

        .annotation-multiple {
          /* Subtle outline effect - kayma yapmaz */
          box-shadow: inset 0 0 0 1px rgba(147, 51, 234, 0.3);
        }

        /* Metin akışını bozmayacak genel kurallar */
        .annotation-paragraph .annotation-highlighted {
          word-break: inherit;
          white-space: inherit;
          text-decoration: inherit;
        }

        /* Annotation ikonları (panel ikonları) */
        .annotation-highlighted .annotation-icon {
          margin-left: 3px;
          vertical-align: baseline;
          display: inline-block;
          /* İkon da alt çizginin içinde kalsın */
          box-shadow: inherit;
        }

        .annotation-highlighted .annotation-icon svg {
          width: 16px;
          height: 16px;
          vertical-align: baseline;
          display: inline-block;
        }

      `
    }} />
  );
}
