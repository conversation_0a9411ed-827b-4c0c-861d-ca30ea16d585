/**
 * 🚀 Text Highlighter - Basit ve Etkili
 * Kural: Üstte olan uygulansın!
 */

import React, { useMemo } from 'react';
import type { Annotation } from '../types/highlight.types';

/**
 * 🛡️ Sentence ID validation - geçersiz ID'leri filtreler
 */
function isValidSentenceId(sentenceId: string): boolean {
  if (!sentenceId || typeof sentenceId !== 'string') {
    return false;
  }

  // Geçersiz pattern'leri kontrol et
  const invalidPatterns = [
    /^invalid-sentence-/,  // "invalid-sentence-" ile başlayanlar
    /^temp-/,              // "temp-" ile başlayanlar
    /^placeholder-/,       // "placeholder-" ile başlayanlar
    /^error-/,             // "error-" ile başlayanlar
  ];

  // Herhangi bir geçersiz pattern eşleşirse false döndür
  for (const pattern of invalidPatterns) {
    if (pattern.test(sentenceId)) {
      return false;
    }
  }

  // Minimum uzunluk kontrolü
  if (sentenceId.length < 3) {
    return false;
  }

  // Maksimum uzunluk kontrolü (çok uzun ID'ler de şüpheli)
  if (sentenceId.length > 50) {
    return false;
  }

  // Geçerli format kontrolü - örnek: "9_1_13" formatı
  const validFormatPattern = /^\d+_\d+_\d+$/;
  if (validFormatPattern.test(sentenceId)) {
    return true;
  }

  // Diğer geçerli formatlar için ek kontroller eklenebilir
  // Şimdilik sadece sayı_sayı_sayı formatını kabul ediyoruz
  return false;
}

/**
 * 🔍 Arapça phrase detection - CSS class'larını tespit eder
 */
function detectArabicPhrases(htmlText: string): Array<{
  plainStart: number;
  plainEnd: number;
  fontSize: string;
  textDecoration: string;
  textDecorationStyle: string;
  textDecorationColor: string;
  textUnderlineOffset: string;
  translation: string;
}> {
  const arabicPhrases: Array<{
    plainStart: number;
    plainEnd: number;
    fontSize: string;
    textDecoration: string;
    textDecorationStyle: string;
    textDecorationColor: string;
    textUnderlineOffset: string;
    translation: string;
  }> = [];

  // interactive-arabic-phrase class'ını içeren span'ları bul
  const arabicRegex = /<span[^>]*class="[^"]*interactive-arabic-phrase[^"]*"[^>]*data-arabic-translation="([^"]*)"[^>]*>(.*?)<\/span>/gi;
  let match;

  // Önce pozisyon mapping'i oluştur
  const tempMap = createPositionMapSimple(htmlText);

  while ((match = arabicRegex.exec(htmlText)) !== null) {
    const fullMatch = match[0];
    const translation = match[1]; // data-arabic-translation değeri
    const content = match[2]; // span içeriği
    const htmlStartIndex = match.index;

    // Span tag'inin içindeki content'in başlangıç pozisyonunu bul
    const contentStartInHtml = htmlStartIndex + fullMatch.indexOf(content);
    const contentEndInHtml = contentStartInHtml + content.length;

    // HTML pozisyonlarını plain text pozisyonlarına çevir
    const plainStart = tempMap.htmlToPlain[contentStartInHtml] || 0;
    const plainEnd = tempMap.htmlToPlain[contentEndInHtml - 1] + 1 || tempMap.plainText.length;

    arabicPhrases.push({
      plainStart,
      plainEnd,
      fontSize: '170%', // CSS'teki değer (font-size: 170%)
      textDecoration: 'underline',
      textDecorationStyle: 'dotted',
      textDecorationColor: 'var(--text-color)',
      textUnderlineOffset: '6px',
      translation: decodeURIComponent(translation) // 🎯 Translation'ı decode et
    });
  }

  return arabicPhrases;
}

/**
 * 🔧 Basit pozisyon mapping (detection için) - BR tag'leri newline olarak işlenir
 */
function createPositionMapSimple(htmlText: string): {
  htmlToPlain: number[];
  plainText: string;
} {
  const htmlToPlain: number[] = [];
  let htmlIndex = 0;
  let plainIndex = 0;
  let plainText = '';

  while (htmlIndex < htmlText.length) {
    const char = htmlText[htmlIndex];

    if (char === '<') {
      const tagEnd = htmlText.indexOf('>', htmlIndex);
      if (tagEnd !== -1) {
        const tagContent = htmlText.substring(htmlIndex, tagEnd + 1);

        // 🔧 BR tag'i için özel işlem - newline karakteri ekle
        if (tagContent.toLowerCase().includes('<br')) {
          // BR tag'ini plain text'te newline olarak temsil et
          htmlToPlain[htmlIndex] = plainIndex;
          plainText += '\n'; // BR tag'ini newline karakteri olarak ekle
          plainIndex++;

          // Tag'in geri kalanını aynı pozisyona map'le
          for (let i = htmlIndex + 1; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex - 1;
          }
        } else {
          // Diğer tag'ler için normal işlem
          for (let i = htmlIndex; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex;
          }
        }
        htmlIndex = tagEnd + 1;
      } else {
        htmlIndex++;
      }
    } else {
      htmlToPlain[htmlIndex] = plainIndex;
      plainText += char;
      plainIndex++;
      htmlIndex++;
    }
  }

  return { htmlToPlain, plainText };
}

/**
 * 🎯 HTML-aware pozisyon hesaplama - BR tag'leri newline olarak işlenir
 */
function createPositionMap(htmlText: string): {
  htmlToPlain: number[];
  plainToHtml: number[];
  plainText: string;
  arabicPhrases: Array<{
    plainStart: number;
    plainEnd: number;
    fontSize: string;
    textDecoration: string;
    textDecorationStyle: string;
    textDecorationColor: string;
    textUnderlineOffset: string;
    translation: string;
  }>;
} {
  const htmlToPlain: number[] = [];
  const plainToHtml: number[] = [];
  let htmlIndex = 0;
  let plainIndex = 0;
  let plainText = '';

  while (htmlIndex < htmlText.length) {
    const char = htmlText[htmlIndex];

    if (char === '<') {
      // HTML tag'i kontrol et
      const tagEnd = htmlText.indexOf('>', htmlIndex);
      if (tagEnd !== -1) {
        const tagContent = htmlText.substring(htmlIndex, tagEnd + 1);

        // 🔧 BR tag'i için özel işlem - newline karakteri ekle
        if (tagContent.toLowerCase().includes('<br')) {
          // BR tag'ini plain text'te newline olarak temsil et
          htmlToPlain[htmlIndex] = plainIndex;
          plainToHtml[plainIndex] = htmlIndex;
          plainText += '\n'; // BR tag'ini newline karakteri olarak ekle
          plainIndex++;

          // Tag'in geri kalanını aynı pozisyona map'le
          for (let i = htmlIndex + 1; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex - 1;
          }
        } else {
          // Diğer tag'ler için normal işlem - plain text'e ekleme
          for (let i = htmlIndex; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex; // Tag karakterleri son plain karaktere point eder
          }
        }
        htmlIndex = tagEnd + 1;
      } else {
        htmlIndex++;
      }
    } else {
      // Normal karakter
      htmlToPlain[htmlIndex] = plainIndex;
      plainToHtml[plainIndex] = htmlIndex;
      plainText += char;
      plainIndex++;
      htmlIndex++;
    }
  }

  // Arapça phrase'leri tespit et
  const arabicPhrases = detectArabicPhrases(htmlText);

  return { htmlToPlain, plainToHtml, plainText, arabicPhrases };
}

/**
 * 🎯 Hybrid Pozisyon Bulma - 3 Aşamalı Fallback
 */
function findTextPosition(
  fullText: string,
  annotation: Annotation
): { start: number; end: number; confidence: 'high' | 'medium' | 'low' } | null {
  const { plainText, htmlToPlain } = createPositionMap(fullText);
  let plainSelectedText = annotation.selected_text.replace(/<[^>]*>/g, '');

  // 🔧 MIGRATION FIX: BR tag'li annotation'ları newline karakterli metne uyarla
  if (plainText.includes('\n') && !plainSelectedText.includes('\n')) {
    // Annotation'da newline yok ama metinde var - BR tag migration sonrası
    // Annotation'ın gerçek pozisyonunu bul ve newline karakterini doğru yere ekle
    const expectedStart = annotation.selection_start;
    const expectedEnd = annotation.selection_end;

    // Annotation'ın kapsadığı metin aralığında newline var mı?
    const annotationRange = plainText.substring(expectedStart, expectedEnd);
    const newlineIndex = annotationRange.indexOf('\n');

    if (newlineIndex !== -1) {
      // Newline karakteri annotation içinde - doğru pozisyona ekle
      const beforeNL = plainSelectedText.substring(0, newlineIndex);
      const afterNL = plainSelectedText.substring(newlineIndex);
      plainSelectedText = beforeNL + '\n' + afterNL;

      console.log('🔧 MIGRATION FIX: Annotation newline karakteri eklendi:', {
        original: annotation.selected_text,
        fixed: plainSelectedText,
        expectedRange: `${expectedStart}-${expectedEnd}`,
        newlineIndex: newlineIndex
      });
    }
  }

  // HTML pozisyonunu plain text pozisyonuna çevir
  const plainExpectedStart = htmlToPlain[annotation.selection_start] || 0;

  // 🚀 AŞAMA 1: Hızlı Pozisyon Kontrolü
  const expectedEnd = plainExpectedStart + plainSelectedText.length;
  if (expectedEnd <= plainText.length) {
    const textAtExpected = plainText.substring(plainExpectedStart, expectedEnd);
    if (textAtExpected === plainSelectedText) {
      console.log('✅ Aşama 1: Hızlı pozisyon bulundu');
      return { start: plainExpectedStart, end: expectedEnd, confidence: 'high' };
    }
  }

  // 🎯 AŞAMA 2: Context-Aware Arama
  if (annotation.prefix_text && annotation.suffix_text) {
    const prefix = annotation.prefix_text.replace(/<[^>]*>/g, '');
    const suffix = annotation.suffix_text.replace(/<[^>]*>/g, '');
    const searchPattern = `${prefix}${plainSelectedText}${suffix}`;

    const foundIndex = plainText.indexOf(searchPattern);
    if (foundIndex !== -1) {
      const actualStart = foundIndex + prefix.length;
      const actualEnd = actualStart + plainSelectedText.length;
      console.log('✅ Aşama 2: Context ile bulundu');
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }

    // Partial context match - sadece prefix ile dene
    const prefixPattern = `${prefix}${plainSelectedText}`;
    const prefixIndex = plainText.indexOf(prefixPattern);
    if (prefixIndex !== -1) {
      const actualStart = prefixIndex + prefix.length;
      const actualEnd = actualStart + plainSelectedText.length;
      console.log('✅ Aşama 2b: Prefix ile bulundu');
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }

    // Sadece suffix ile dene
    const suffixPattern = `${plainSelectedText}${suffix}`;
    const suffixIndex = plainText.indexOf(suffixPattern);
    if (suffixIndex !== -1) {
      const actualStart = suffixIndex;
      const actualEnd = actualStart + plainSelectedText.length;
      console.log('✅ Aşama 2c: Suffix ile bulundu');
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }
  }

  // 🔍 AŞAMA 3: Fuzzy Search (Çoklu Eşleşme Kontrolü)
  const allMatches: number[] = [];
  let searchIndex = 0;

  while (searchIndex < plainText.length) {
    const foundIndex = plainText.indexOf(plainSelectedText, searchIndex);
    if (foundIndex === -1) break;

    allMatches.push(foundIndex);
    searchIndex = foundIndex + 1;
  }

  if (allMatches.length === 1) {
    // Tek eşleşme - güvenli
    console.log('✅ Aşama 3: Tek eşleşme bulundu');
    return {
      start: allMatches[0],
      end: allMatches[0] + plainSelectedText.length,
      confidence: 'low'
    };
  } else if (allMatches.length > 1) {
    // Çoklu eşleşme - en yakınını seç
    const closest = allMatches.reduce((prev, curr) => {
      return Math.abs(curr - plainExpectedStart) < Math.abs(prev - plainExpectedStart) ? curr : prev;
    });

    console.log(`⚠️ Aşama 3: ${allMatches.length} eşleşme, en yakını seçildi`);
    return {
      start: closest,
      end: closest + plainSelectedText.length,
      confidence: 'low'
    };
  }

  // ❌ AŞAMA 4: Başarısızlık
  console.warn('❌ Annotation bulunamadı:', {
    annotation_id: annotation.id,
    selected_text: annotation.selected_text,
    expected_start: annotation.selection_start,
    has_prefix: !!annotation.prefix_text,
    has_suffix: !!annotation.suffix_text,
    // 🔧 DEBUG: Newline karakteri debug bilgisi
    plainText_preview: plainText.substring(0, 100).replace(/\n/g, '\\n'),
    plainSelectedText: plainSelectedText.replace(/\n/g, '\\n'),
    has_newlines_in_text: plainText.includes('\n'),
    has_newlines_in_selection: plainSelectedText.includes('\n')
  });

  return null;
}

/**
 * 🛡️ HTML segment'ini güvenli şekilde çıkar - DOMParser kullanarak
 */
function extractHtmlSegment(htmlText: string, startPos: number, endPos: number): string {
  try {
    // Basit fallback: pozisyon aralığındaki text'i al
    const segment = htmlText.substring(startPos, endPos + 1);

    // Eğer segment HTML tag içermiyorsa direkt döndür
    if (!segment.includes('<')) {
      return segment;
    }

    // HTML tag'leri varsa güvenli parsing
    return parseHtmlSegmentSafely(htmlText, startPos, endPos);

  } catch (error) {
    // Hata durumunda basit substring döndür
    console.warn('HTML segment extraction failed, using fallback:', error);
    return htmlText.substring(startPos, endPos + 1);
  }
}

/**
 * 🔒 Güvenli HTML segment parsing
 */
function parseHtmlSegmentSafely(htmlText: string, startPos: number, endPos: number): string {
  // Segment'i al
  let segment = htmlText.substring(startPos, endPos + 1);

  // Açık tag'leri takip et (basit stack yaklaşımı)
  const openTags: string[] = [];

  // Segment öncesindeki açık tag'leri bul
  const beforeSegment = htmlText.substring(0, startPos);
  const beforeTags = extractTagsFromText(beforeSegment);

  // Açık tag'leri hesapla
  for (const tag of beforeTags) {
    if (tag.isClosing) {
      const index = openTags.lastIndexOf(tag.name);
      if (index !== -1) openTags.splice(index, 1);
    } else if (!tag.isSelfClosing) {
      openTags.push(tag.name);
    }
  }

  // Segment içindeki tag'leri analiz et
  const segmentTags = extractTagsFromText(segment);
  const segmentOpenTags: string[] = [];

  for (const tag of segmentTags) {
    if (tag.isClosing) {
      const index = segmentOpenTags.lastIndexOf(tag.name);
      if (index !== -1) segmentOpenTags.splice(index, 1);
    } else if (!tag.isSelfClosing) {
      segmentOpenTags.push(tag.name);
    }
  }

  // Açık tag'leri segment başına ekle
  for (const tagName of openTags) {
    segment = `<${tagName}>${segment}`;
  }

  // Kapatılmamış tag'leri kapat
  for (let i = segmentOpenTags.length - 1; i >= 0; i--) {
    segment = `${segment}</${segmentOpenTags[i]}>`;
  }

  // Açık tag'leri kapat
  for (let i = openTags.length - 1; i >= 0; i--) {
    segment = `${segment}</${openTags[i]}>`;
  }

  return segment;
}

/**
 * 🏷️ Text'ten tag'leri güvenli şekilde çıkar
 */
function extractTagsFromText(text: string): Array<{
  name: string;
  isClosing: boolean;
  isSelfClosing: boolean;
}> {
  const tags: Array<{ name: string; isClosing: boolean; isSelfClosing: boolean }> = [];
  const selfClosingTags = new Set(['br', 'img', 'hr', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr']);

  // Basit regex - güvenlik için sınırlı
  const tagRegex = /<(\/?)([\w-]+)(?:\s[^>]*)?(\/?)?>/g;
  let match;

  while ((match = tagRegex.exec(text)) !== null) {
    const isClosing = match[1] === '/';
    const tagName = match[2].toLowerCase();
    const isSelfClosing = selfClosingTags.has(tagName) || match[3] === '/';

    tags.push({
      name: tagName,
      isClosing,
      isSelfClosing
    });
  }

  return tags;
}

/**
 * 🎯 Multi-sentence annotation'ları bu sentence için işle
 */
function processMultiSentenceAnnotations(
  annotations: Annotation[],
  currentSentenceId: string,
  currentSentenceText: string,
  allSentences: Array<{ id: string; text: string }>
): Annotation[] {
  const processedAnnotations: Annotation[] = [];

  for (const annotation of annotations) {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    // 🛡️ Geçersiz sentence ID'leri filtrele
    const validSentenceIds = sentenceIds.filter(id => isValidSentenceId(id));

    if (validSentenceIds.length === 0) {
      continue; // Geçersiz annotation, sessizce atla
    }

    // Bu annotation bu sentence'ı içeriyor mu?
    if (!validSentenceIds.includes(currentSentenceId)) {
      continue; // Bu sentence için değil, atla
    }

    if (validSentenceIds.length === 1) {
      // Single sentence annotation - direkt ekle
      processedAnnotations.push(annotation);
    } else {
      // Multi-sentence annotation - bu sentence için pozisyonu hesapla
      // 🛡️ Geçerli sentence ID'leri ile çalış
      const adjustedAnnotation = adjustAnnotationForSentence(
        { ...annotation, sentence_id: validSentenceIds }, // Geçerli ID'lerle yeni annotation
        currentSentenceId,
        currentSentenceText,
        allSentences
      );

      if (adjustedAnnotation) {
        processedAnnotations.push(adjustedAnnotation);
      }
    }
  }

  return processedAnnotations;
}

/**
 * 🔧 Multi-sentence annotation'ı bu sentence için ayarla
 */
function adjustAnnotationForSentence(
  annotation: Annotation,
  currentSentenceId: string,
  currentSentenceText: string,
  allSentences: Array<{ id: string; text: string }>
): Annotation | null {
  try {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    // Bu sentence'ın index'ini bul
    const currentSentenceIndex = sentenceIds.indexOf(currentSentenceId);
    if (currentSentenceIndex === -1) {
      return null; // Bu sentence annotation'da yok
    }

    // Tüm sentence'ları birleştir (orijinal selection'ı yeniden oluştur)
    // NOT: Space eklemeyin, çünkü orijinal pozisyonlar space olmadan hesaplanmış
    const fullText = sentenceIds
      .map(id => allSentences.find(s => s.id === id)?.text || '')
      .join('');

    // Bu sentence'ın full text içindeki pozisyonunu hesapla
    let sentenceStartInFullText = 0;
    for (let i = 0; i < currentSentenceIndex; i++) {
      const prevSentence = allSentences.find(s => s.id === sentenceIds[i]);
      if (prevSentence) {
        // HTML tag'lerini kaldırarak plain text length hesapla
        const plainText = prevSentence.text.replace(/<[^>]*>/g, '');
        sentenceStartInFullText += plainText.length;
      }
    }

    // Current sentence'ın plain text length'ini hesapla
    const currentSentencePlainText = currentSentenceText.replace(/<[^>]*>/g, '');
    const sentenceEndInFullText = sentenceStartInFullText + currentSentencePlainText.length;

    // Annotation'ın bu sentence ile kesişen kısmını hesapla
    const annotationStart = annotation.selection_start;
    const annotationEnd = annotation.selection_end;

    // Kesişim var mı?
    const intersectionStart = Math.max(annotationStart, sentenceStartInFullText);
    const intersectionEnd = Math.min(annotationEnd, sentenceEndInFullText);

    if (intersectionStart >= intersectionEnd) {
      return null; // Kesişim yok
    }

    // Bu sentence içindeki relative pozisyonları hesapla
    const relativeStart = intersectionStart - sentenceStartInFullText;
    const relativeEnd = intersectionEnd - sentenceStartInFullText;

    // Selected text'i bu sentence için ayarla - Plain text bazında
    const adjustedSelectedText = currentSentencePlainText.substring(relativeStart, relativeEnd);

    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Multi-sentence adjustment:', {
        annotationId: annotation.id,
        currentSentenceId,
        currentSentenceIndex,
        sentenceIds,
        fullText: fullText.substring(0, 100) + '...',
        sentenceStartInFullText,
        sentenceEndInFullText,
        annotationStart,
        annotationEnd,
        intersectionStart,
        intersectionEnd,
        relativeStart,
        relativeEnd,
        currentSentenceText: currentSentenceText.substring(0, 50) + '...',
        currentSentencePlainText: currentSentencePlainText.substring(0, 50) + '...',
        adjustedSelectedText,
        originalSelectedText: annotation.selected_text,
        hasIntersection: intersectionStart < intersectionEnd
      });
    }

    // Yeni annotation oluştur
    return {
      ...annotation,
      selection_start: relativeStart,
      selection_end: relativeEnd,
      selected_text: adjustedSelectedText,
      // Orijinal multi-sentence bilgisini koru
      _original_sentence_ids: sentenceIds,
      _original_selection_start: annotationStart,
      _original_selection_end: annotationEnd,
      _original_selected_text: annotation.selected_text
    } as Annotation;

  } catch (error) {
    console.warn('Multi-sentence annotation adjustment failed:', error);
    return null;
  }
}

interface TextHighlighterProps {
  text: string;
  annotations: Annotation[];
  onAnnotationClick?: (annotation: Annotation) => void;
  sentenceId: string; // Bu sentence'ın ID'si
  allSentences?: Array<{ id: string; text: string }>; // Tüm sentence'lar (multi-sentence için)
}

export const TextHighlighter: React.FC<TextHighlighterProps> = ({
  text,
  annotations,
  onAnnotationClick,
  sentenceId,
  allSentences = []
}) => {
  const styledText = useMemo(() => {
    if (!text || !annotations || annotations.length === 0) {
      // 🔧 FIXED: CSS white-space: pre-line ile newline karakterleri otomatik handle ediliyor
      return <span dangerouslySetInnerHTML={{ __html: text }} style={{ whiteSpace: 'pre-line' }} />;
    }

    // HTML-aware pozisyon mapping
    const { plainText, plainToHtml, arabicPhrases } = createPositionMap(text);

    // Her karakter için stil hesapla (plain text bazında)
    const chars = Array.from(plainText);
    const charStyles: Array<{
      backgroundColor?: string;
      color?: string;
      underline?: string;
      fontSize?: string;
      textDecoration?: string;
      textDecorationStyle?: string;
      textDecorationColor?: string;
      textUnderlineOffset?: string;
      translation?: string;
      annotation?: Annotation;
    }> = new Array(chars.length).fill(null).map(() => ({}));

    // 🎨 Arapça phrase'lerin stillerini ayarla
    arabicPhrases.forEach(phrase => {
      for (let i = phrase.plainStart; i < phrase.plainEnd && i < chars.length; i++) {
        charStyles[i].fontSize = phrase.fontSize;
        charStyles[i].textDecoration = phrase.textDecoration;
        charStyles[i].textDecorationStyle = phrase.textDecorationStyle;
        charStyles[i].textDecorationColor = phrase.textDecorationColor;
        charStyles[i].textUnderlineOffset = phrase.textUnderlineOffset;
        charStyles[i].translation = phrase.translation; // 🎯 Translation'ı sakla
      }
    });

    // 🎯 MULTI-SENTENCE ANNOTATION PROCESSING
    const processedAnnotations = processMultiSentenceAnnotations(
      annotations,
      sentenceId,
      text,
      allSentences
    );

    // Annotation'ları öncelik sırasına göre sırala - SON YAPILAN KAZANIR
    const sortedAnnotations = [...processedAnnotations].sort((a, b) => {
      // Öncelik sırası: highlight → bookmark → note (note en üstte görünür)
      const priority = { highlight: 1, bookmark: 2, note: 3 };
      const priorityDiff = priority[a.annotation_type] - priority[b.annotation_type];

      // Aynı türdeyse created_at'e göre sırala
      if (priorityDiff === 0) {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }

      return priorityDiff;
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 TextHighlighter Debug:', {
        sentenceId,
        text: text.substring(0, 50) + '...',
        originalAnnotationsCount: annotations.length,
        processedAnnotationsCount: processedAnnotations.length,
        multiSentenceCount: annotations.filter(a => Array.isArray(a.sentence_id) && a.sentence_id.length > 1).length,
        processedAnnotations: processedAnnotations.map(a => ({
          id: a.id,
          type: a.annotation_type,
          start: a.selection_start,
          end: a.selection_end,
          selected_text: a.selected_text,
          isAdjusted: !!a._original_sentence_ids
        }))
      });
    }

    // Her annotation için karakterleri işaretle - SON YAPILAN ÜZERİNE YAZAR
    sortedAnnotations.forEach(annotation => {
      // 🎯 HYBRID POZİSYON BULMA - 3 Aşamalı Fallback
      const result = findTextPosition(text, annotation);

      if (result === null) {
        return; // Bu annotation'ı atla
      }

      const { start, end, confidence } = result;

      console.log('✅ Annotation bulundu:', {
        annotation_id: annotation.id,
        selected_text: annotation.selected_text,
        original_pos: `${annotation.selection_start}-${annotation.selection_end}`,
        actual_pos: `${start}-${end}`,
        confidence: confidence,
        found_text: plainText.substring(start, end),
        // 🔧 DEBUG: Newline karakteri debug bilgisi
        plainText_preview: plainText.substring(0, 100).replace(/\n/g, '\\n'),
        has_newlines: plainText.includes('\n'),
        newline_count: (plainText.match(/\n/g) || []).length
      });

      for (let i = start; i < end && i < chars.length; i++) {
        // Mevcut stil varsa koru, sadece yeni özellikleri ekle
        const existingStyle = charStyles[i] || {};
        const newStyle: any = { ...existingStyle };

        switch (annotation.annotation_type) {
          case 'note':
            newStyle.underline = '#fbbf24'; // Sarı alt çizgi
            newStyle.backgroundColor = '#fbbf2430'; // Sarı arka plan (30% opacity)
            newStyle.annotation = annotation;
            break;
          case 'bookmark':
            newStyle.underline = '#10b981'; // Yeşil alt çizgi
            newStyle.backgroundColor = '#10b98130'; // Yeşil arka plan (30% opacity)
            newStyle.annotation = annotation;
            break;
          case 'highlight':
            if (annotation.highlight_style === 'background') {
              newStyle.backgroundColor = annotation.color + '40'; // Sadece arka plan vurgusu
            } else {
              newStyle.color = annotation.color; // Sadece metin rengi
            }
            newStyle.annotation = annotation;
            break;
        }

        charStyles[i] = newStyle; // SON YAPILAN KAZANIR
      }
    });

    // Ardışık aynı stilleri birleştir (performance için)
    const textSegments: Array<{
      text: string;
      style: React.CSSProperties;
      annotation?: Annotation;
      translation?: string;
      plainStart?: number;
      plainEnd?: number;
    }> = [];

    let currentSegment = {
      text: '',
      style: {} as React.CSSProperties,
      annotation: undefined as Annotation | undefined,
      translation: undefined as string | undefined,
      plainStart: 0,
      plainEnd: 0
    };

    chars.forEach((char, index) => {
      const charStyle = charStyles[index];
      const cssStyle: React.CSSProperties = {};

      if (charStyle.backgroundColor) {
        cssStyle.backgroundColor = charStyle.backgroundColor;
      }
      if (charStyle.color) {
        cssStyle.color = charStyle.color;
      }
      if (charStyle.underline) {
        cssStyle.boxShadow = `inset 0 -2px 0 0 ${charStyle.underline}`;
      }
      if (charStyle.fontSize) {
        cssStyle.fontSize = charStyle.fontSize; // 🎨 Arapça font-size korunuyor
      }
      if (charStyle.textDecoration) {
        cssStyle.textDecoration = charStyle.textDecoration; // 🎨 Arapça underline korunuyor
      }
      if (charStyle.textDecorationStyle) {
        cssStyle.textDecorationStyle = charStyle.textDecorationStyle as any; // 🎨 Noktalı stil korunuyor
      }
      if (charStyle.textDecorationColor) {
        cssStyle.textDecorationColor = charStyle.textDecorationColor; // 🎨 Underline rengi korunuyor
      }
      if (charStyle.textUnderlineOffset) {
        cssStyle.textUnderlineOffset = charStyle.textUnderlineOffset; // 🎨 Underline offset korunuyor
      }
      if (charStyle.annotation) {
        cssStyle.cursor = 'pointer';
      }

      // Stil aynı mı? - Performance optimized comparison
      const isSameStyle = (
        cssStyle.backgroundColor === currentSegment.style.backgroundColor &&
        cssStyle.color === currentSegment.style.color &&
        cssStyle.boxShadow === currentSegment.style.boxShadow &&
        cssStyle.fontSize === currentSegment.style.fontSize &&
        cssStyle.textDecoration === currentSegment.style.textDecoration &&
        cssStyle.textDecorationStyle === currentSegment.style.textDecorationStyle &&
        cssStyle.textDecorationColor === currentSegment.style.textDecorationColor &&
        cssStyle.textUnderlineOffset === currentSegment.style.textUnderlineOffset &&
        cssStyle.cursor === currentSegment.style.cursor
      );

      const isSameTranslation = charStyle.translation === currentSegment.translation;

      if (isSameStyle && isSameTranslation && charStyle.annotation?.id === currentSegment.annotation?.id) {
        // Aynı stil, segment'e ekle
        currentSegment.text += char;
        currentSegment.plainEnd = index + 1;
      } else {
        // Farklı stil, yeni segment başlat
        if (currentSegment.text) {
          // Plain text pozisyonlarını HTML pozisyonlarına çevir
          const htmlStart = plainToHtml[currentSegment.plainStart] || 0;
          const htmlEnd = plainToHtml[currentSegment.plainEnd - 1] || text.length;

          // HTML segment'ini çıkar - TAG'LERİ KORU
          currentSegment.text = extractHtmlSegment(text, htmlStart, htmlEnd);
          textSegments.push({ ...currentSegment });
        }
        currentSegment = {
          text: char,
          style: cssStyle,
          annotation: charStyle.annotation,
          translation: charStyle.translation,
          plainStart: index,
          plainEnd: index + 1
        };
      }
    });

    // Son segment'i ekle
    if (currentSegment.text) {
      // Plain text pozisyonlarını HTML pozisyonlarına çevir
      const htmlStart = plainToHtml[currentSegment.plainStart] || 0;
      const htmlEnd = plainToHtml[currentSegment.plainEnd - 1] || text.length;

      // HTML segment'ini çıkar - TAG'LERİ KORU
      currentSegment.text = extractHtmlSegment(text, htmlStart, htmlEnd);
      textSegments.push(currentSegment);
    }

    return (
      <>
        {textSegments.map((segment, index) => {
          // 🎯 Arapça phrase için özel props
          const isArabicPhrase = !!segment.translation;

          // HTML tag'leri varsa dangerouslySetInnerHTML, yoksa normal text rendering
          const hasHtmlTags = segment.text.includes('<') && !segment.text.match(/^[^<]*$/);

          const spanProps: any = {
            style: { ...segment.style, whiteSpace: 'pre-line' } // Newline karakterlerini koru
          };

          if (hasHtmlTags) {
            // HTML tag'leri varsa dangerouslySetInnerHTML kullan
            spanProps.dangerouslySetInnerHTML = { __html: segment.text };
          } else {
            // 🔧 FIXED: CSS white-space: pre-line ile newline karakterleri otomatik handle ediliyor
            spanProps.dangerouslySetInnerHTML = { __html: segment.text };
          }

          if (isArabicPhrase) {
            // Arapça phrase için interactive class ve data attribute ekle
            spanProps.className = 'interactive-arabic-phrase';
            spanProps['data-arabic-translation'] = encodeURIComponent(segment.translation!);
            spanProps.style = { ...spanProps.style, cursor: 'pointer' };
          }

          if (segment.annotation) {
            // Annotation için data attribute ekle (silme işlemi için gerekli)
            spanProps['data-annotation-id'] = segment.annotation.id;

            // Normal annotation click handler (sadece Arapça phrase değilse)
            if (!isArabicPhrase) {
              spanProps.onClick = (e: React.MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                onAnnotationClick?.(segment.annotation!);
              };
            }

            // Annotation ikonunu metne ekle (sadece note ve bookmark için)
            const getAnnotationIcon = () => {
              switch (segment.annotation!.annotation_type) {
                case 'note':
                  return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#fbbf24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>`;
                case 'bookmark':
                  return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/></svg>`;
                case 'highlight':
                  return ''; // Normal highlight'larda ikon yok
                default: return '';
              }
            };

            // Mevcut HTML içeriğine ikonu ekle (sadece varsa)
            if (spanProps.dangerouslySetInnerHTML) {
              const icon = getAnnotationIcon();
              if (icon) {
                spanProps.dangerouslySetInnerHTML.__html += `<span class="annotation-icon" style="margin-left: 3px; vertical-align: baseline; display: inline-block;">${icon}</span>`;
              }
            }
          }

          return <span key={index} {...spanProps} />;
        })}
      </>
    );
  }, [text, annotations, onAnnotationClick]);

  return <>{styledText}</>;
};

// 🔧 CSS stilleri için yardımcı component - newline karakterlerini destekler
export function TextHighlighterStyles() {
  return (
    <style dangerouslySetInnerHTML={{
      __html: `
        /* TextHighlighter için genel stiller */
        .text-highlighter-container {
          white-space: pre-line; /* Newline karakterlerini korur */
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        /* Annotation'lı span'lar için stiller */
        .text-highlighter-container span {
          white-space: pre-line; /* Newline karakterlerini korur */
        }

        /* Interactive Arabic phrase stiller korunuyor */
        .text-highlighter-container .interactive-arabic-phrase {
          cursor: pointer;
          font-size: 170%;
          text-decoration: underline;
          text-decoration-style: dotted;
          text-decoration-color: var(--text-color);
          text-underline-offset: 6px;
          white-space: pre-line; /* Newline karakterlerini korur */
        }

        /* Annotation hover efektleri */
        .text-highlighter-container span[data-annotation-id]:hover {
          opacity: 0.8;
          transition: opacity 0.2s ease;
        }

        /* Annotation ikonları (panel ikonları) */
        .text-highlighter-container .annotation-icon {
          margin-left: 3px;
          vertical-align: baseline;
          display: inline-block;
          /* İkon da alt çizginin içinde kalsın */
          box-shadow: inherit;
        }

        .text-highlighter-container .annotation-icon svg {
          width: 16px;
          height: 16px;
          vertical-align: baseline;
          display: inline-block;
        }
      `
    }} />
  );
}

export default TextHighlighter;
