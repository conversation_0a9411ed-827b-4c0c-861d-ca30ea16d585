import { useState, useEffect, useRef } from 'react';
import { X, Save, MessageSquare, Highlighter, Bookmark } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { Annotation, UpdateAnnotationInput } from '../../shared/types';
import { useAnnotationManager } from '../hooks/useAnnotationManager';

interface AnnotationEditModalProps {
  annotation: Annotation;
  isOpen: boolean;
  onClose: () => void;
  onUpdated: (updatedAnnotation: Annotation) => void;
}

export function AnnotationEditModal({
  annotation,
  isOpen,
  onClose,
  onUpdated
}: AnnotationEditModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const { updateAnnotation } = useAnnotationManager();

  // Theme-aware colors
  const modalBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const headerBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = useAutoOverlay(8, 'var(--bg-color)');
  const inputBgColor = useAutoOverlay(6, 'var(--bg-color)');

  // Form state
  const [content, setContent] = useState(annotation.annotation_content || '');
  const [tags, setTags] = useState<string[]>(annotation.tags || []);
  const [tagInput, setTagInput] = useState('');
  const [isPublic, setIsPublic] = useState(annotation.is_public || false);
  const [isLoading, setIsLoading] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setContent(annotation.annotation_content || '');
      setTags(annotation.tags || []);
      setTagInput('');
      setIsPublic(annotation.is_public || false);
    }
  }, [isOpen, annotation]);

  // Close modal on escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Close modal on backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Tag management
  const addTag = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Form submission
  const handleSubmit = async () => {
    if (isLoading) return;

    // Note için content zorunlu
    if (annotation.annotation_type === 'note' && !content.trim()) {
      alert('Şerh içeriği boş olamaz');
      return;
    }

    try {
      setIsLoading(true);

      const updateData: UpdateAnnotationInput = {
        annotation_content: content.trim() || undefined,
        tags,
        is_public: isPublic,
        metadata: annotation.metadata || {}
      };

      const updatedAnnotation = await updateAnnotation(annotation.id, updateData);

      if (updatedAnnotation) {
        onUpdated(updatedAnnotation);
      } else {
        alert('Şerh güncellenirken hata oluştu');
      }
    } catch (error) {
      console.error('[AnnotationEditModal] Update error:', error);
      alert('Şerh güncellenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  // Annotation type icon
  const getTypeIcon = () => {
    switch (annotation.annotation_type) {
      case 'note':
        return <MessageSquare size={20} className="text-blue-600" />;
      case 'highlight':
        return <Highlighter size={20} className="text-yellow-600" />;
      case 'bookmark':
        return <Bookmark size={20} className="text-green-600" />;
      default:
        return <MessageSquare size={20} className="text-gray-600" />;
    }
  };

  // Annotation type label
  const getTypeLabel = () => {
    switch (annotation.annotation_type) {
      case 'note':
        return 'Şerh';
      case 'highlight':
        return 'Vurgulama';
      case 'bookmark':
        return 'Yer İmi';
      default:
        return 'Annotation';
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="relative w-full max-w-lg rounded-xl shadow-2xl transform transition-all duration-300 max-h-[90vh] overflow-hidden"
        style={{ backgroundColor: modalBgColor }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div
          className="flex items-center justify-between p-4 border-b"
          style={{
            backgroundColor: headerBgColor,
            borderColor: borderColor
          }}
        >
          <div className="flex items-center space-x-2">
            {getTypeIcon()}
            <div>
              <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
                {getTypeLabel()} Düzenle
              </h2>
              <p className="text-sm" style={{ color: 'var(--text-color)', opacity: 0.7 }}>Şerhinizi güncelleyin</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full transition-colors"
            style={{
              color: 'var(--text-color)',
              opacity: 0.7
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Selected Text Preview */}
          <div
            className="p-4 border-b"
            style={{
              backgroundColor: headerBgColor,
              borderColor: borderColor
            }}
          >
            <p className="text-sm mb-2" style={{ color: 'var(--text-color)', opacity: 0.7 }}>Seçilen Metin:</p>
            <div
              className="p-3 rounded-lg border-l-4 text-sm"
              style={{
                backgroundColor: inputBgColor,
                borderColor: '#3b82f6',
                color: 'var(--text-color)'
              }}
            >
              "{annotation.selected_text.length > 200
                ? annotation.selected_text.substring(0, 200) + '...'
                : annotation.selected_text}"
            </div>
          </div>

          {/* Form */}
          <div className="p-4 space-y-4">
            {/* Content Input */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>
                {annotation.annotation_type === 'note' ? 'Şerh İçeriği *' : 'Açıklama (Opsiyonel)'}
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={
                  annotation.annotation_type === 'note'
                    ? 'Şerhinizi yazın...'
                    : `${annotation.annotation_type === 'highlight' ? 'Vurgulama' : 'Yer imi'} için opsiyonel açıklama...`
                }
                className="w-full h-24 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundColor: inputBgColor,
                  borderColor: borderColor,
                  color: 'var(--text-color)'
                }}
                rows={3}
                autoFocus
              />
              {annotation.annotation_type === 'note' && !content.trim() && (
                <p className="text-xs text-red-500 mt-1">* Şerh içeriği zorunludur</p>
              )}
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>Etiketler</label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagInputKeyDown}
                    placeholder="Etiket ekle..."
                    className="flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    style={{
                      backgroundColor: inputBgColor,
                      borderColor: borderColor,
                      color: 'var(--text-color)'
                    }}
                  />
                  <button
                    onClick={addTag}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    Ekle
                  </button>
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Public/Private Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>Görünürlük</label>
                <p className="text-xs" style={{ color: 'var(--text-color)', opacity: 0.7 }}>Şerhinizi diğer kullanıcılarla paylaşın</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isPublic}
                  onChange={(e) => setIsPublic(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                <span className="ml-3 text-sm" style={{ color: 'var(--text-color)' }}>
                  {isPublic ? 'Herkese Açık' : 'Özel'}
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div
          className="flex space-x-3 p-4 border-t"
          style={{
            backgroundColor: headerBgColor,
            borderColor: borderColor
          }}
        >
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 py-3 px-4 border rounded-lg disabled:opacity-50 transition-colors font-medium"
            style={{
              backgroundColor: inputBgColor,
              borderColor: borderColor,
              color: 'var(--text-color)'
            }}
          >
            İptal
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || (annotation.annotation_type === 'note' && !content.trim())}
            className="flex-2 flex items-center justify-center space-x-2 py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold shadow-md hover:shadow-lg"
          >
            <Save size={18} />
            <span className="text-base">
              {isLoading ? 'Güncelleniyor...' : 'Güncelle'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
