import { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Clock, MessageSquare, ArrowUpDown, ChevronRight, ExternalLink } from 'lucide-react';
import { supabase } from '@shared/utils/supabaseClient';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { LoadingState, EmptyState } from '@shared/components';
// ✅ YENİ ORGANİZE YAPISI - Shared components
import { AnnotationErrorBoundary } from '../../shared/components/AnnotationErrorBoundary';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { Annotation } from '../../shared/types';

interface AnnotationSearchSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  sentenceIds: string[]; // Seçilen sentence ID'leri
  bookId?: string;
  sectionId?: string;
  onViewAll?: (searchParams: {
    selectedText: string;
    sentenceIds: string[];
    bookId?: string;
    sectionId?: string;
  }) => void; // Yeni sayfa için callback
}

type SortOption = 'faydalı' | 'yeni' | 'eski';

export function AnnotationSearchSheet({
  isOpen,
  onClose,
  selectedText,
  sentenceIds,
  bookId,
  sectionId,
  onViewAll
}: AnnotationSearchSheetProps) {
  const navigate = useNavigate();
  const [sortBy, setSortBy] = useState<SortOption>('faydalı');

  // Theme-aware colors
  const sheetBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const headerBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const separatorBorderColor = useAutoOverlay(8, 'var(--bg-color)');
  const buttonBgColor = useAutoOverlay(8, 'var(--bg-color)');



  // Kendi annotation state'i
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Annotation'ları yükle
  const loadAnnotations = async () => {
    if (!isOpen || sentenceIds.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      console.log('[AnnotationSearchSheet] Loading annotations for sentences:', sentenceIds);
      console.log('[AnnotationSearchSheet] Selected text:', selectedText);

      // Daha geniş arama için birden fazla strateji kullan
      const allAnnotations: Annotation[] = [];

      // 1. Sentence ID bazlı arama - sadece şerhler
      const { data: sentenceBasedData, error: sentenceError } = await supabase
        .from('text_annotations')
        .select('*')
        .or(
          sentenceIds.map(id => `sentence_id.cs.["${id}"]`).join(',')
        )
        .eq('book_id', bookId || '')
        .eq('section_id', sectionId || '')
        .eq('annotation_type', 'note'); // Sadece şerhleri getir

      if (!sentenceError && sentenceBasedData) {
        allAnnotations.push(...sentenceBasedData);
      }

      // 2. Aynı bölümdeki tüm şerhleri de getir (text-based matching için)
      const { data: sectionData, error: sectionError } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('book_id', bookId || '')
        .eq('section_id', sectionId || '')
        .eq('annotation_type', 'note'); // Sadece şerhleri getir

      if (!sectionError && sectionData) {
        // Duplicate'leri önlemek için ID kontrolü yap
        const existingIds = new Set(allAnnotations.map(a => a.id));
        const newAnnotations = sectionData.filter(a => !existingIds.has(a.id));
        allAnnotations.push(...newAnnotations);
      }

      const data = allAnnotations;

      // Hata kontrolü
      if (sentenceError && sectionError) {
        console.error('[AnnotationSearchSheet] Both queries failed:', { sentenceError, sectionError });
        setError(`Annotation'lar yüklenemedi: ${sentenceError.message || sectionError.message}`);
        return;
      }

      console.log('[AnnotationSearchSheet] Raw annotations loaded:', data?.length || 0);

      // Text matching - çok daha esnek ve kapsamlı eşleştirme (sadece şerhler)
      const matchingAnnotations = (data || []).filter(annotation => {
        // Sadece şerhleri dahil et
        if (annotation.annotation_type !== 'note') {
          return false;
        }

        const annotationText = annotation.selected_text.toLowerCase().trim();
        const searchText = selectedText.toLowerCase().trim();

        console.log('[AnnotationSearchSheet] Comparing:', {
          annotationText: annotationText.substring(0, 50),
          searchText: searchText.substring(0, 50),
          annotationId: annotation.id,
          type: annotation.annotation_type
        });

        // 1. Tam eşleşme (en yüksek öncelik)
        if (annotationText === searchText) {
          console.log('[AnnotationSearchSheet] Exact match found:', annotation.id);
          return true;
        }

        // 2. Birbirini içerme kontrolü (daha esnek)
        if (annotationText.includes(searchText) || searchText.includes(annotationText)) {
          console.log('[AnnotationSearchSheet] Contains match found:', annotation.id);
          return true;
        }

        // 3. Sentence ID overlap kontrolü
        const annotationSentenceIds = Array.isArray(annotation.sentence_id)
          ? annotation.sentence_id
          : [annotation.sentence_id];

        const hasCommonSentences = sentenceIds.some(id => annotationSentenceIds.includes(id));

        if (hasCommonSentences) {
          console.log('[AnnotationSearchSheet] Common sentence found:', annotation.id);
          return true; // Ortak sentence varsa direkt dahil et
        }

        // 4. Kelime bazlı benzerlik (çok daha esnek)
        const annotationWords = annotationText.split(/\s+/).filter((w: string) => w.length > 2);
        const searchWords = searchText.split(/\s+/).filter((w: string) => w.length > 2);

        if (annotationWords.length > 0 && searchWords.length > 0) {
          // Daha esnek kelime eşleştirme
          const commonWords = annotationWords.filter((word: string) =>
            searchWords.some((searchWord: string) => {
              // Tam eşleşme
              if (word === searchWord) return true;
              // Birbirini içerme (3+ karakter için)
              if (word.length >= 3 && searchWord.length >= 3) {
                return word.includes(searchWord) || searchWord.includes(word);
              }
              return false;
            })
          );

          const similarity = commonWords.length / Math.min(annotationWords.length, searchWords.length);

          if (similarity >= 0.1) { // %10 benzerlik (çok daha esnek)
            console.log('[AnnotationSearchSheet] Word similarity match found:', annotation.id, similarity);
            return true;
          }
        }

        // 5. Anahtar kelime araması (en az 3 karakter)
        if (searchText.length >= 3) {
          const searchKeywords = searchText.split(/\s+/).filter(w => w.length >= 3);
          const hasKeyword = searchKeywords.some(keyword =>
            annotationText.includes(keyword)
          );

          if (hasKeyword) {
            console.log('[AnnotationSearchSheet] Keyword match found:', annotation.id);
            return true;
          }
        }

        return false;
      });

      console.log('[AnnotationSearchSheet] Matching annotations:', matchingAnnotations.length);
      setAnnotations(matchingAnnotations);

    } catch (err) {
      console.error('[AnnotationSearchSheet] Unexpected error:', err);
      setError('Beklenmeyen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Sheet açıldığında annotation'ları yükle
  useEffect(() => {
    loadAnnotations();
  }, [isOpen, sentenceIds, selectedText, bookId, sectionId]);

  // Preview için limit (hibrit yaklaşım)
  const PREVIEW_LIMIT = 3;

  // Sadece sıralama
  const filteredAndSortedAnnotations = useMemo(() => {
    const sorted = [...annotations].sort((a, b) => {
      switch (sortBy) {
        case 'yeni':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'eski':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'faydalı':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    return sorted;
  }, [annotations, sortBy]);

  // Preview için sadece ilk birkaç annotation
  const previewAnnotations = filteredAndSortedAnnotations.slice(0, PREVIEW_LIMIT);
  const hasMoreAnnotations = filteredAndSortedAnnotations.length > PREVIEW_LIMIT;

  // Debug log
  console.log('[AnnotationSearchSheet] Preview debug:', {
    totalAnnotations: filteredAndSortedAnnotations.length,
    previewLimit: PREVIEW_LIMIT,
    hasMoreAnnotations,
    onViewAllExists: !!onViewAll
  });

  const handleViewAll = () => {
    if (onViewAll) {
      onViewAll({
        selectedText,
        sentenceIds,
        bookId,
        sectionId
      });
    }
  };



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Bugün';
    if (diffInDays === 1) return 'Dün';
    if (diffInDays < 7) return `${diffInDays} gün önce`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} hafta önce`;
    return date.toLocaleDateString('tr-TR');
  };

  const getAnnotationTypeName = (type: string) => {
    switch (type) {
      case 'note': return 'Şerh';
      case 'highlight': return 'Vurgulama';
      case 'bookmark': return 'Yer İmi';
      default: return 'Bilinmeyen';
    }
  };

  // Seçilen metni paragraf yapısını koruyarak formatla
  const formatSelectedText = (annotation: any) => {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    if (sentenceIds.length === 1) {
      // Tek sentence - normal göster
      return annotation.selected_text;
    }

    // Çoklu sentence - her sentence'ı ayrı paragraf olarak göster
    const sentences = [];
    for (const sentenceId of sentenceIds) {
      const element = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
      if (element) {
        const sentenceText = element.textContent || '';
        if (sentenceText.trim()) {
          sentences.push(sentenceText.trim());
        }
      }
    }

    return sentences;
  };

  // Annotation detay sayfasına git
  const handleAnnotationClick = (annotation: Annotation) => {
    navigate(`/annotations/${annotation.id}?return=${encodeURIComponent('/annotations/search')}&returnText=${encodeURIComponent('Arama Sonuçlarına Dön')}`);
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Sadece backdrop'a tıklanırsa kapat (target === currentTarget)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-end justify-center md:items-center"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]" />

      {/* Sheet */}
      <div
        className="relative w-full max-w-2xl max-h-[85vh] md:max-h-[75vh] md:rounded-2xl rounded-t-2xl shadow-2xl overflow-hidden border border-opacity-20"
        style={{
          backgroundColor: sheetBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Header */}
        <div
          className="flex items-center justify-between p-6 border-b"
          style={{
            backgroundColor: headerBgColor,
            borderColor: borderColor
          }}
        >
          <div className="flex-1">
            <h2 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-color)' }}>
              Şerh Arama Sonuçları
            </h2>
            <div
              className="p-3 rounded-lg border-l-4 text-sm"
              style={{
                backgroundColor: cardBgColor,
                borderLeftColor: '#fbbf24',
                color: 'var(--text-color)',
                opacity: 0.9
              }}
            >
              <p className="italic font-medium">
                "{selectedText.length > 80 ? selectedText.substring(0, 80) + '...' : selectedText}"
              </p>
            </div>
            <div className="flex items-center gap-2 mt-3">
              <MessageSquare size={16} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
              <span className="text-sm font-medium" style={{ color: 'var(--text-color)', opacity: 0.8 }}>
                {filteredAndSortedAnnotations.length} şerh bulundu
                {hasMoreAnnotations && (
                  <span className="text-xs ml-2" style={{ opacity: 0.6 }}>
                    (İlk {PREVIEW_LIMIT} tanesi gösteriliyor)
                  </span>
                )}
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-xl transition-all duration-200 hover:scale-110"
            style={{
              backgroundColor: cardBgColor,
              color: 'var(--text-color)'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Sort */}
        <div
          className="flex items-center justify-between p-4 border-b"
          style={{
            backgroundColor: headerBgColor,
            borderColor: separatorBorderColor
          }}
        >
          <div className="flex items-center gap-3">
            <ArrowUpDown size={16} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className="px-3 py-2 rounded-lg border transition-all duration-200 focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50"
              style={{
                backgroundColor: cardBgColor,
                borderColor: borderColor,
                color: 'var(--text-color)'
              }}
            >
              <option value="faydalı">En Faydalı</option>
              <option value="yeni">En Yeni</option>
              <option value="eski">En Eski</option>
            </select>
          </div>
        </div>

        {/* Content */}
        <div
          className="overflow-y-auto max-h-96 overscroll-contain"
          style={{
            WebkitOverflowScrolling: 'touch',
            touchAction: 'pan-y'
          }}
        >
          {loading ? (
            <LoadingState message="Şerhler yükleniyor..." />
          ) : error ? (
            <div
              className="p-8 text-center rounded-xl m-4"
              style={{
                backgroundColor: cardBgColor,
                borderColor: borderColor
              }}
            >
              <div className="text-4xl mb-4">❌</div>
              <h3 className="text-lg font-semibold mb-2 text-red-500">Hata oluştu</h3>
              <p className="text-sm mb-6" style={{ color: 'var(--text-color)', opacity: 0.7 }}>{error}</p>
              <button
                onClick={loadAnnotations}
                className="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-all duration-200 font-medium shadow-md hover:shadow-lg"
              >
                Tekrar Dene
              </button>
            </div>
          ) : filteredAndSortedAnnotations.length === 0 ? (
            <EmptyState message="Bu metin için şerh bulunamadı" />
          ) : (
            <AnnotationErrorBoundary>
              <div>
                {/* Preview Annotations */}
                <div className="grid gap-4 p-4">
                  {previewAnnotations.map((annotation) => {
                const hasLongContent = (annotation.annotation_content?.length || 0) > 200;

                return (
                  <div
                    key={annotation.id}
                    className="p-5 rounded-xl border transition-all duration-300 cursor-pointer hover:shadow-lg hover:scale-[1.02]"
                    style={{
                      backgroundColor: cardBgColor,
                      borderColor: separatorBorderColor,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={() => handleAnnotationClick(annotation)}
                  >
                    {/* Annotation Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="p-2.5 rounded-lg"
                          style={{ background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)' }}
                        >
                          <MessageSquare size={16} className="text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-sm" style={{ color: 'var(--text-color)' }}>
                            {getAnnotationTypeName(annotation.annotation_type)}
                          </h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Clock size={12} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
                            <span className="text-xs" style={{ color: 'var(--text-color)', opacity: 0.6 }}>
                              {formatDate(annotation.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Detay İpucu */}
                      <ChevronRight size={16} style={{ color: 'var(--text-color)', opacity: 0.4 }} />
                    </div>

                    {/* Selected Text */}
                    <div className="mb-4">
                      <div
                        className="p-3 rounded-lg border-l-4 text-sm"
                        style={{
                          backgroundColor: headerBgColor,
                          borderLeftColor: '#fbbf24',
                          color: 'var(--text-color)'
                        }}
                      >
                        <p className="italic font-medium leading-relaxed">
                          {(() => {
                            const formattedText = formatSelectedText(annotation);
                            if (typeof formattedText === 'string') {
                              return `"${formattedText}"`;
                            } else {
                              return `"${formattedText[0]}"${formattedText.length > 1 ? '...' : ''}`;
                            }
                          })()}
                        </p>
                      </div>
                    </div>

                    {/* Annotation Content */}
                    {annotation.annotation_content && (
                      <div className="border-t pt-4" style={{ borderColor: separatorBorderColor }}>
                        <div className="text-sm break-words whitespace-pre-wrap leading-relaxed" style={{ color: 'var(--text-color)' }}>
                          {hasLongContent
                            ? `${annotation.annotation_content.substring(0, 200)}...`
                            : annotation.annotation_content
                          }
                        </div>
                        {hasLongContent && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAnnotationClick(annotation);
                            }}
                            className="mt-3 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 inline-flex items-center gap-2"
                            style={{
                              backgroundColor: buttonBgColor,
                              color: '#fbbf24'
                            }}
                          >
                            <span>Devamını Oku</span>
                            <ExternalLink size={14} />
                          </button>
                        )}
                      </div>
                    )}

                  </div>
                );
              })}
              </div>

              {/* "Tümünü Gör" Butonu */}
              {hasMoreAnnotations && onViewAll && (
                <div
                  className="p-4 border-t"
                  style={{
                    backgroundColor: headerBgColor,
                    borderColor: separatorBorderColor
                  }}
                >
                  <button
                    onClick={handleViewAll}
                    className="w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg flex items-center justify-center gap-3"
                    style={{
                      background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                      color: 'white'
                    }}
                  >
                    <span>Tümünü Gör ({filteredAndSortedAnnotations.length} şerh)</span>
                    <ExternalLink size={18} />
                  </button>
                  <p className="text-xs text-center mt-3" style={{ color: 'var(--text-color)', opacity: 0.6 }}>
                    Gelişmiş arama ve filtreleme seçenekleri ile
                  </p>
                </div>
              )}
            </div>
          </AnnotationErrorBoundary>
        )}
        </div>
      </div>
    </div>
  );
}
