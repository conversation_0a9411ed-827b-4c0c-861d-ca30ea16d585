import { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { SortDesc } from 'lucide-react';
import { supabase } from '@shared/utils/supabaseClient';
import { PageLayout } from '@shared/components/organisms/Layout/PageLayout';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
// ✅ YENİ ORGANİZE YAPISI - Shared components
import { AnnotationSkeleton } from '../../shared/components/AnnotationSkeleton';
import { AnnotationErrorBoundary } from '../../shared/components/AnnotationErrorBoundary';

// ✅ YENİ ORGANİZE YAPISI - Types from shared
import type { Annotation } from '../../shared/types';

type SortOption = 'newest' | 'oldest' | 'most_helpful' | 'author';

export function AnnotationSearchPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // URL'den parametreleri al
  const selectedText = searchParams.get('text') || '';
  const sentenceIds = searchParams.get('sentences')?.split(',') || [];
  const bookId = searchParams.get('book') || '';
  const sectionId = searchParams.get('section') || '';

  // Theme-aware colors
  const cardBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const headerBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = useAutoOverlay(8, 'var(--bg-color)');
  // State
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<SortOption>('most_helpful');

  // Annotation'ları yükle
  const loadAnnotations = async () => {
    if (sentenceIds.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      console.log('[AnnotationSearchPage] Loading annotations for sentences:', sentenceIds);

      // Daha geniş arama için birden fazla strateji kullan
      const allAnnotations: any[] = [];

      // 1. Sentence ID bazlı arama - sadece şerhler
      const { data: sentenceBasedData, error: sentenceError } = await supabase
        .from('text_annotations')
        .select('*')
        .or(
          sentenceIds.map(id => `sentence_id.cs.["${id}"]`).join(',')
        )
        .eq('book_id', bookId)
        .eq('section_id', sectionId)
        .eq('annotation_type', 'note'); // Sadece şerhleri getir

      if (!sentenceError && sentenceBasedData) {
        allAnnotations.push(...sentenceBasedData);
      }

      // 2. Aynı bölümdeki tüm şerhleri de getir
      const { data: sectionData, error: sectionError } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('book_id', bookId)
        .eq('section_id', sectionId)
        .eq('annotation_type', 'note'); // Sadece şerhleri getir

      if (!sectionError && sectionData) {
        // Duplicate'leri önlemek için ID kontrolü yap
        const existingIds = new Set(allAnnotations.map(a => a.id));
        const newAnnotations = sectionData.filter(a => !existingIds.has(a.id));
        allAnnotations.push(...newAnnotations);
      }

      const data = allAnnotations;

      // Hata kontrolü
      if (sentenceError && sectionError) {
        console.error('[AnnotationSearchPage] Both queries failed:', { sentenceError, sectionError });
        setError(`Annotation'lar yüklenemedi: ${sentenceError.message || sectionError.message}`);
        return;
      }

      console.log('[AnnotationSearchPage] Raw annotations loaded:', data?.length || 0);

      // Text matching - çok daha esnek ve kapsamlı eşleştirme (sadece şerhler)
      const matchingAnnotations = (data || []).filter(annotation => {
        // Sadece şerhleri dahil et
        if (annotation.annotation_type !== 'note') {
          return false;
        }

        const annotationText = annotation.selected_text.toLowerCase().trim();
        const searchText = selectedText.toLowerCase().trim();

        console.log('[AnnotationSearchPage] Comparing:', {
          annotationText: annotationText.substring(0, 50),
          searchText: searchText.substring(0, 50),
          annotationId: annotation.id,
          type: annotation.annotation_type
        });

        // 1. Tam eşleşme (en yüksek öncelik)
        if (annotationText === searchText) {
          console.log('[AnnotationSearchPage] Exact match found:', annotation.id);
          return true;
        }

        // 2. Birbirini içerme kontrolü (daha esnek)
        if (annotationText.includes(searchText) || searchText.includes(annotationText)) {
          console.log('[AnnotationSearchPage] Contains match found:', annotation.id);
          return true;
        }

        // 3. Sentence ID overlap kontrolü - ama text benzerliği de olmalı
        const annotationSentenceIds = Array.isArray(annotation.sentence_id)
          ? annotation.sentence_id
          : [annotation.sentence_id];

        const hasCommonSentences = sentenceIds.some(id => annotationSentenceIds.includes(id));

        if (hasCommonSentences) {
          console.log('[AnnotationSearchPage] Common sentence found:', annotation.id);
          return true; // Ortak sentence varsa direkt dahil et
        }

        // 4. Kelime bazlı benzerlik (çok daha esnek)
        const annotationWords = annotationText.split(/\s+/).filter((w: string) => w.length > 2);
        const searchWords = searchText.split(/\s+/).filter((w: string) => w.length > 2);

        if (annotationWords.length > 0 && searchWords.length > 0) {
          // Daha esnek kelime eşleştirme
          const commonWords = annotationWords.filter((word: string) =>
            searchWords.some((searchWord: string) => {
              // Tam eşleşme
              if (word === searchWord) return true;
              // Birbirini içerme (3+ karakter için)
              if (word.length >= 3 && searchWord.length >= 3) {
                return word.includes(searchWord) || searchWord.includes(word);
              }
              return false;
            })
          );

          const similarity = commonWords.length / Math.min(annotationWords.length, searchWords.length);

          if (similarity >= 0.1) { // %10 benzerlik (çok daha esnek)
            console.log('[AnnotationSearchPage] Word similarity match found:', annotation.id, similarity);
            return true;
          }
        }

        // 5. Anahtar kelime araması (en az 3 karakter)
        if (searchText.length >= 3) {
          const searchKeywords = searchText.split(/\s+/).filter(w => w.length >= 3);
          const hasKeyword = searchKeywords.some(keyword =>
            annotationText.includes(keyword)
          );

          if (hasKeyword) {
            console.log('[AnnotationSearchPage] Keyword match found:', annotation.id);
            return true;
          }
        }

        return false;
      });

      console.log('[AnnotationSearchPage] Matching annotations:', matchingAnnotations.length);
      setAnnotations(matchingAnnotations);

    } catch (err) {
      console.error('[AnnotationSearchPage] Unexpected error:', err);
      setError('Beklenmeyen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnnotations();
  }, []);

  // Sıralama uygula
  const filteredAndSortedAnnotations = useMemo(() => {
    const sorted = [...annotations].sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'most_helpful':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'author':
          return (a.user_id || '').localeCompare(b.user_id || '');
        default:
          return 0;
      }
    });

    return sorted;
  }, [annotations, sortBy]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Bugün';
    if (diffInDays === 1) return 'Dün';
    if (diffInDays < 7) return `${diffInDays} gün önce`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} hafta önce`;
    return date.toLocaleDateString('tr-TR');
  };

  const getAnnotationTypeIcon = (type: string) => {
    switch (type) {
      case 'note': return '📝';
      case 'highlight': return '🖍️';
      case 'bookmark': return '🔖';
      default: return '📄';
    }
  };

  const getAnnotationTypeName = (type: string) => {
    switch (type) {
      case 'note': return 'Şerh';
      case 'highlight': return 'Vurgulama';
      case 'bookmark': return 'Yer İmi';
      default: return 'Bilinmeyen';
    }
  };

  const formatSelectedText = (annotation: any) => {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    if (sentenceIds.length === 1) {
      return annotation.selected_text;
    }

    // Çoklu sentence için paragraf yapısını koru
    const sentences = [];
    for (const sentenceId of sentenceIds) {
      const element = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
      if (element) {
        const sentenceText = element.textContent || '';
        if (sentenceText.trim()) {
          sentences.push(sentenceText.trim());
        }
      }
    }

    return sentences;
  };

  // Annotation detay sayfasına git
  const handleAnnotationClick = (annotation: Annotation) => {
    const currentUrl = window.location.pathname + window.location.search;
    navigate(`/annotations/${annotation.id}?return=${encodeURIComponent(currentUrl)}&returnText=${encodeURIComponent('Arama Sonuçlarına Dön')}`);
  };

  return (
    <PageLayout
      title="Şerh Arama"
      showBackButton={true}
    >
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Sort and Results */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            {/* Sort */}
            <div className="flex items-center space-x-2">
              <SortDesc size={16} style={{ color: 'var(--text-color)', opacity: 0.5 }} />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundColor: cardBgColor,
                  borderColor: borderColor,
                  color: 'var(--text-color)'
                }}
              >
                <option value="most_helpful">En Faydalı</option>
                <option value="newest">En Yeni</option>
                <option value="oldest">En Eski</option>
                <option value="author">Yazara Göre</option>
              </select>
            </div>

            {/* Results count */}
            <p className="text-sm" style={{ color: 'var(--text-color)', opacity: 0.7 }}>
              {filteredAndSortedAnnotations.length} şerh bulundu
            </p>
          </div>
        </div>

        {/* Results */}
        {loading ? (
          <AnnotationSkeleton count={5} />
        ) : error ? (
          <div
            className="rounded-lg border p-8 text-center"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor
            }}
          >
            <div className="text-4xl mb-4">❌</div>
            <h3 className="text-lg font-medium mb-2 text-red-600">Hata oluştu</h3>
            <p className="mb-4" style={{ color: 'var(--text-color)', opacity: 0.7 }}>{error}</p>
            <button
              onClick={loadAnnotations}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Tekrar Dene
            </button>
          </div>
        ) : filteredAndSortedAnnotations.length === 0 ? (
          <div
            className="rounded-lg border p-8 text-center"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor
            }}
          >
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-color)' }}>Şerh bulunamadı</h3>
            <p style={{ color: 'var(--text-color)', opacity: 0.7 }}>Arama kriterlerinize uygun şerh bulunamadı.</p>
          </div>
        ) : (
          <AnnotationErrorBoundary>
            <div className="space-y-4">
              {filteredAndSortedAnnotations.map((annotation) => {
                const hasLongContent = (annotation.annotation_content?.length || 0) > 200;

                return (
                  <div
                    key={annotation.id}
                    className="rounded-lg border p-4 hover:shadow-sm transition-all cursor-pointer"
                    style={{
                      backgroundColor: cardBgColor,
                      borderColor: borderColor
                    }}
                    onClick={() => handleAnnotationClick(annotation)}
                  >
                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getAnnotationTypeIcon(annotation.annotation_type)}</span>
                        <div>
                          <span className="font-medium" style={{ color: 'var(--text-color)' }}>
                            {getAnnotationTypeName(annotation.annotation_type)}
                          </span>
                          <div className="text-xs mt-1" style={{ color: 'var(--text-color)', opacity: 0.6 }}>
                            {formatDate(annotation.created_at)}
                          </div>
                        </div>
                      </div>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: 'var(--text-color)', opacity: 0.4 }}>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>

                    {/* Selected Text */}
                    <div className="mb-3">
                      <div
                        className="p-3 rounded border-l-4 border-blue-400 text-sm italic"
                        style={{
                          backgroundColor: headerBgColor,
                          color: 'var(--text-color)',
                          opacity: 0.8
                        }}
                      >
                        {(() => {
                          const formattedText = formatSelectedText(annotation);
                          if (typeof formattedText === 'string') {
                            return `"${formattedText}"`;
                          } else {
                            return `"${formattedText[0]}"${formattedText.length > 1 ? '...' : ''}`;
                          }
                        })()}
                      </div>
                    </div>

                    {/* Annotation Content */}
                    {annotation.annotation_content && (
                      <div className="text-sm break-words whitespace-pre-wrap" style={{ color: 'var(--text-color)' }}>
                        {hasLongContent
                          ? `${annotation.annotation_content.substring(0, 300)}...`
                          : annotation.annotation_content
                        }
                        {hasLongContent && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAnnotationClick(annotation);
                            }}
                            className="ml-2 text-blue-600 hover:text-blue-800 text-xs font-medium"
                          >
                            Detay
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </AnnotationErrorBoundary>
        )}
      </div>
    </PageLayout>
  );
}
