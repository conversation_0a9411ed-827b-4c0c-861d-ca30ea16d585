import { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Edit3,
  Trash2,
  ExternalLink,
  Calendar,
  User,
  MessageSquare,
  Highlighter,
  Bookmark,
  Tag,
  Globe,
  Lock,
  Copy,
  Share2
} from 'lucide-react';
import { useAuthStore } from '@domains/auth/store/authStore';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { useAnnotationManager } from '../hooks/useAnnotationManager';
import { supabase } from '@shared/utils/supabaseClient';
import { PageLayout } from '@shared/components/organisms/Layout/PageLayout';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import type { Annotation } from '../../shared/types';
import { AnnotationEditModal } from '../components/AnnotationEditModal';

export function AnnotationDetailPage() {
  const { annotationId } = useParams<{ annotationId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { deleteAnnotation } = useAnnotationManager();

  // Theme-aware colors
  const cardBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const headerBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = useAutoOverlay(8, 'var(--bg-color)');
  const hoverBgColor = useAutoOverlay(6, 'var(--bg-color)');

  // State
  const [annotation, setAnnotation] = useState<Annotation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // URL'den geri dönüş bilgileri
  const returnUrl = searchParams.get('return') || '/';
  // const returnText = searchParams.get('returnText') || 'Geri Dön'; // Şimdilik kullanılmıyor

  // Annotation'ı yükle
  useEffect(() => {
    if (!annotationId) {
      setError('Annotation ID bulunamadı');
      setLoading(false);
      return;
    }

    loadAnnotation();
  }, [annotationId]);

  const loadAnnotation = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: queryError } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('id', annotationId)
        .single();

      if (queryError) {
        throw queryError;
      }

      if (!data) {
        setError('Annotation bulunamadı');
        return;
      }

      setAnnotation(data);
    } catch (err) {
      console.error('[AnnotationDetailPage] Load error:', err);
      setError('Annotation yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Geri dön
  const handleGoBack = () => {
    if (returnUrl === '/') {
      navigate(-1);
    } else {
      navigate(returnUrl);
    }
  };

  // Orijinal metne git
  const handleGoToOriginal = () => {
    if (!annotation) return;

    const { book_id, section_id, sentence_id } = annotation;
    
    // Sentence ID'yi array'den string'e çevir
    const firstSentenceId = Array.isArray(sentence_id) ? sentence_id[0] : sentence_id;
    
    // Risale okuma sayfasına git
    const originalUrl = `/risale/${book_id}/${section_id}#sentence-${firstSentenceId}`;
    navigate(originalUrl);
  };

  // Düzenleme modal'ını aç
  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  // Annotation'ı sil
  const handleDelete = async () => {
    if (!annotation || !user || annotation.user_id !== user.id) return;

    const confirmed = window.confirm('Bu şerhi silmek istediğinizden emin misiniz?');
    if (!confirmed) return;

    try {
      setIsDeleting(true);
      const success = await deleteAnnotation(annotation.id);
      
      if (success) {
        // Silme başarılı, geri dön
        handleGoBack();
      } else {
        alert('Şerh silinirken hata oluştu');
      }
    } catch (err) {
      console.error('[AnnotationDetailPage] Delete error:', err);
      alert('Şerh silinirken hata oluştu');
    } finally {
      setIsDeleting(false);
    }
  };

  // Metni kopyala
  const handleCopyText = async () => {
    if (!annotation) return;

    try {
      await navigator.clipboard.writeText(annotation.selected_text);
      // TODO: Toast notification ekle
      alert('Metin kopyalandı!');
    } catch (err) {
      console.error('Copy error:', err);
      alert('Metin kopyalanamadı');
    }
  };

  // Annotation'ı paylaş
  const handleShare = async () => {
    if (!annotation) return;

    const shareUrl = `${window.location.origin}/annotations/${annotation.id}`;
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Şerh',
          text: annotation.annotation_content || annotation.selected_text,
          url: shareUrl
        });
      } else {
        await navigator.clipboard.writeText(shareUrl);
        alert('Link kopyalandı!');
      }
    } catch (err) {
      console.error('Share error:', err);
    }
  };

  // Annotation güncelleme callback'i
  const handleAnnotationUpdated = (updatedAnnotation: Annotation) => {
    setAnnotation(updatedAnnotation);
    setIsEditModalOpen(false);
  };

  // Loading state
  if (loading) {
    return (
      <PageLayout title="Şerh Detayı" showBackButton={true}>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-600">Şerh yükleniyor...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Error state
  if (error || !annotation) {
    return (
      <PageLayout title="Hata" showBackButton={true}>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="text-4xl mb-4">❌</div>
            <h3 className="text-lg font-medium mb-2 text-red-600">Hata</h3>
            <p className="text-gray-600 mb-4">{error || 'Annotation bulunamadı'}</p>
            <button
              onClick={handleGoBack}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Geri Dön
            </button>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Kullanıcı kendi annotation'ı mı?
  const isOwner = user && annotation.user_id === user.id;

  // Annotation type icon
  const getTypeIcon = () => {
    switch (annotation.annotation_type) {
      case 'note':
        return <MessageSquare size={20} className="text-blue-600" />;
      case 'highlight':
        return <Highlighter size={20} className="text-yellow-600" />;
      case 'bookmark':
        return <Bookmark size={20} className="text-green-600" />;
      default:
        return <MessageSquare size={20} className="text-gray-600" />;
    }
  };

  // Annotation type label
  const getTypeLabel = () => {
    switch (annotation.annotation_type) {
      case 'note':
        return 'Şerh';
      case 'highlight':
        return 'Vurgulama';
      case 'bookmark':
        return 'Yer İmi';
      default:
        return 'Annotation';
    }
  };

  return (
    <PageLayout
      title={`${getTypeLabel()} Detayı`}
      showBackButton={true}
    >

      {/* Content */}
      <div className="max-w-3xl mx-auto px-4 py-8">
        {/* Header with Icon and Actions */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-3">
            {getTypeIcon()}
            <div>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--text-color)' }}>{getTypeLabel()}</h1>
              <div className="flex items-center space-x-4 text-sm mt-1" style={{ color: 'var(--text-color)', opacity: 0.7 }}>
                <div className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>
                    {new Date(annotation.created_at).toLocaleDateString('tr-TR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User size={14} />
                  <span>{isOwner ? 'Sizin şerhiniz' : 'Anonim kullanıcı'}</span>
                </div>
                {annotation.is_public ? (
                  <div className="flex items-center space-x-1 text-green-600">
                    <Globe size={14} />
                    <span>Herkese Açık</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1">
                    <Lock size={14} />
                    <span>Özel</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleGoToOriginal}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm"
              style={{
                backgroundColor: hoverBgColor,
                borderColor: borderColor,
                color: 'var(--text-color)'
              }}
            >
              <ExternalLink size={14} />
              <span className="hidden sm:inline">Orijinal Metne Git</span>
            </button>

            {isOwner && (
              <>
                <button
                  onClick={handleEdit}
                  className="flex items-center space-x-2 px-3 py-2 text-blue-600 hover:text-blue-700 rounded-lg hover:bg-blue-50 transition-colors text-sm border"
                  style={{ borderColor: borderColor }}
                >
                  <Edit3 size={14} />
                  <span className="hidden sm:inline">Düzenle</span>
                </button>

                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="flex items-center space-x-2 px-3 py-2 text-red-600 hover:text-red-700 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50 text-sm border"
                  style={{ borderColor: borderColor }}
                >
                  <Trash2 size={14} />
                  <span className="hidden sm:inline">{isDeleting ? 'Siliniyor...' : 'Sil'}</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Main Content Card */}
        <div
          className="rounded-xl shadow-sm border overflow-hidden"
          style={{
            backgroundColor: cardBgColor,
            borderColor: borderColor
          }}
        >
          {/* Seçilen Metin */}
          <div
            className="p-6 border-b"
            style={{
              backgroundColor: headerBgColor,
              borderColor: borderColor
            }}
          >
            <h3 className="text-sm font-semibold mb-3" style={{ color: 'var(--text-color)', opacity: 0.8 }}>Seçilen Metin</h3>
            <blockquote
              className="leading-relaxed italic text-lg border-l-4 pl-4"
              style={{
                color: 'var(--text-color)',
                borderColor: '#3b82f6'
              }}
            >
              "{annotation.selected_text}"
            </blockquote>
          </div>

          {/* Annotation İçeriği */}
          {annotation.annotation_content && (
            <div className="p-6">
              <h3 className="text-sm font-semibold mb-4" style={{ color: 'var(--text-color)', opacity: 0.8 }}>{getTypeLabel()} İçeriği</h3>
              <div className="prose max-w-none">
                <p
                  className="leading-relaxed whitespace-pre-wrap text-base"
                  style={{ color: 'var(--text-color)' }}
                >
                  {annotation.annotation_content}
                </p>
              </div>
            </div>
          )}

          {/* Etiketler */}
          {(annotation.tags && annotation.tags.length > 0) && (
            <div className="px-6 pb-6">
              <div className="flex items-center space-x-2 mb-3">
                <Tag size={16} style={{ color: 'var(--text-color)', opacity: 0.7 }} />
                <span className="text-sm font-semibold" style={{ color: 'var(--text-color)', opacity: 0.8 }}>Etiketler</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {annotation.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Aksiyon Butonları */}
          <div className="px-6 pb-6">
            <div
              className="flex items-center space-x-3 pt-4 border-t"
              style={{ borderColor: borderColor }}
            >
              <button
                onClick={handleCopyText}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors"
                style={{
                  backgroundColor: hoverBgColor,
                  color: 'var(--text-color)'
                }}
              >
                <Copy size={16} />
                <span>Metni Kopyala</span>
              </button>

              <button
                onClick={handleShare}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors"
                style={{
                  backgroundColor: hoverBgColor,
                  color: 'var(--text-color)'
                }}
              >
                <Share2 size={16} />
                <span>Paylaş</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {isEditModalOpen && annotation && (
        <AnnotationEditModal
          annotation={annotation}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onUpdated={handleAnnotationUpdated}
        />
      )}
    </PageLayout>
  );
}
