export const FORM_STYLES = {
  input: {
    borderRadius: '12px',
    padding: '12px 16px',
    fontSize: '14px',
    border: '1px solid',
    outline: 'none',
    transition: 'all 0.2s ease',
  },
  button: {
    borderRadius: '12px',
    padding: '12px 16px',
    fontSize: '14px',
    fontWeight: '500',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  },
  container: {
    maxWidth: '400px',
    margin: '0 auto',
    padding: '24px',
  }
};

export const USERNAME_VALIDATION = {
  minLength: 4,
  maxLength: 20,
  pattern: /^[a-zA-Z0-9_]+$/,
  reservedWords: ['admin', 'root', 'user', 'test', 'api', 'www', 'mail', 'support'],
  messages: {
    required: 'Kullanıcı adı gereklidir',
    minLength: 'Kullanıcı adı en az 4 karakter olmalıdır',
    maxLength: '<PERSON><PERSON><PERSON><PERSON><PERSON> adı en fazla 20 karakter olabilir',
    pattern: '<PERSON><PERSON>ıc<PERSON> adı sadece harf, rakam ve alt çizgi (_) içerebilir',
    reserved: 'Bu kullanıcı adı kullanılamaz',
    taken: 'Bu kullanıcı adı zaten alınmış'
  }
};
