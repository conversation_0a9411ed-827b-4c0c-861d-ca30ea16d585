export const FORM_STYLES = {
  input: {
    base: 'w-full px-4 py-3 rounded-xl text-sm focus:outline-none border transition-all',
    normal: 'border-gray-300 focus:border-blue-500',
    error: 'border-red-500 focus:border-red-500',
    success: 'border-green-500 focus:border-green-500',
    borderRadius: '12px',
    padding: '12px 16px',
    fontSize: '14px',
    border: '1px solid',
    outline: 'none',
    transition: 'all 0.2s ease',
  },
  button: {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',
    borderRadius: '12px',
    padding: '12px 16px',
    fontSize: '14px',
    fontWeight: '500',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  },
  container: {
    maxWidth: '400px',
    margin: '0 auto',
    padding: '24px',
  },
  spacing: 'space-y-4'
};

export const USERNAME_VALIDATION = {
  minLength: 4,
  maxLength: 20,
  pattern: /^[a-zA-Z0-9_]+$/,
  reservedWords: ['admin', 'root', 'user', 'test', 'api', 'www', 'mail', 'support'],
  messages: {
    required: 'Kullanıcı adı gereklidir',
    minLength: 'Kullanıcı adı en az 4 karakter olmalıdır',
    maxLength: 'Kullanıcı adı en fazla 20 karakter olabilir',
    pattern: 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir',
    reserved: 'Bu kullanıcı adı kullanılamaz',
    taken: 'Bu kullanıcı adı zaten alınmış'
  },
  errorMessages: {
    tooShort: 'Kullanıcı adı en az 4 karakter olmalıdır',
    tooLong: 'Kullanıcı adı en fazla 20 karakter olabilir',
    invalidChars: 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir'
  }
};
