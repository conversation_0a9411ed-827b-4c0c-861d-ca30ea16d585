import { useState, ChangeEvent, FormEvent, useCallback, useEffect, useRef } from 'react';
import { IAuthFormState } from '../models/types';
import { useAuth } from './useAuth';
import { authService } from '../services/authService';
import debounce from 'lodash/debounce';

// Arayüzü genişletelim
interface IAuthFormStateExtended extends IAuthFormState {
  isCheckingUsername: boolean;
  usernameAvailable: boolean | null; // null: kontrol edilmedi, true: uygun, false: alınmış
}

// Form başlangıç durumu
const initialFormState: IAuthFormStateExtended = {
  email: '',
  password: '',
  confirmPassword: '',
  username: '',
  displayName: '',
  errors: {},
  isSubmitting: false,
  isCheckingUsername: false,
  usernameAvailable: null,
};

// Form doğrulama (username availability kontrolü ha<PERSON>)
const validateForm = (formState: IAuthFormState, isSignup: boolean): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  // Email validasyonu
  if (!formState.email) {
    errors.email = 'Email adresi gereklidir';
  } else if (!/\S+@\S+\.\S+/.test(formState.email)) {
    errors.email = 'Geçerli bir email adresi giriniz';
  }
  
  // Şifre validasyonu
  if (!formState.password) {
    errors.password = 'Şifre gereklidir';
  } else if (formState.password.length < 6) {
    errors.password = 'Şifre en az 6 karakter olmalıdır';
  }
  
  // Kayıt olurken şifre kontrolü
  if (isSignup) {
    if (!formState.confirmPassword) {
      errors.confirmPassword = 'Şifre onayı gereklidir';
    } else if (formState.password !== formState.confirmPassword) {
      errors.confirmPassword = 'Şifreler eşleşmiyor';
    }
    
    // Kullanıcı adı zorunlu ve format validasyonu
    if (!formState.username || !formState.username.trim()) {
      errors.username = 'Kullanıcı adı gereklidir';
    } else {
      const username = formState.username.startsWith('@') ? 
        formState.username.substring(1) : formState.username;
      
      if (username.length < 3) {
        errors.username = 'Kullanıcı adı en az 3 karakter olmalıdır';
      } 
      // Boşluk kontrolü
      else if (/\s/.test(username)) {
        errors.username = 'Kullanıcı adı boşluk içeremez';
      }
      else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        errors.username = 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir';
      }
    } // Availability hatası burada kontrol edilmiyor, state'den gelecek
    
    // Görünen isim zorunlu ve validasyonu
    if (!formState.displayName || !formState.displayName.trim()) {
      errors.displayName = 'Ad ve soyad gereklidir';
    } else if (formState.displayName.length < 2) {
      errors.displayName = 'İsim en az 2 karakter olmalıdır';
    }
  }
  
  return errors;
};

// Login form hook'u
export const useLoginForm = () => {
  const [formState, setFormState] = useState<IAuthFormState>({
    email: '',
    password: '',
    errors: {},
    isSubmitting: false,
  });
  const { login, error: authError, clearError } = useAuth();
  
  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: value,
      errors: {
        ...prev.errors,
        [name]: undefined,
        form: undefined
      }
    }));
    if (clearError) clearError();
  }, [clearError]);
  
  const handleSubmit = useCallback(async (e: FormEvent<HTMLFormElement>, onSuccess?: () => void) => {
    e.preventDefault();
    const errors = validateForm(formState, false); // Sadece email/şifre validasyonu
    if (Object.keys(errors).length > 0) {
      setFormState(prev => ({ ...prev, errors }));
      return;
    }
    setFormState(prev => ({ ...prev, isSubmitting: true, errors: {} }));
    try {
      await login(formState.email, formState.password);
      setFormState({ email: '', password: '', errors: {}, isSubmitting: false });
      if (onSuccess) onSuccess();
    } catch {
      setFormState(prev => ({ 
        ...prev, 
        isSubmitting: false,
        errors: { form: authError || 'Giriş başarısız oldu.' }
      }));
    }
  }, [login, authError, formState]);
  
  return { formState, handleChange, handleSubmit, authError };
};

// Signup form hook'u (güncellenmiş)
export const useSignupForm = () => {
  const [formState, setFormState] = useState<IAuthFormStateExtended>(initialFormState);
  const { signup, error: authError, clearError } = useAuth();
  const usernameInputRef = useRef(formState.username); // Debounce için son değeri takip et

  // Kullanıcı adını doğrula ve kontrol et
  const validateUsername = useCallback(async (value: string) => {
    if (!value) return '';
    
    const cleaned = value.startsWith('@') ? value.substring(1) : value;
    
    // Boş kontrolü
    if (!cleaned || cleaned.trim() === '') {
      return 'Kullanıcı adı boş olamaz';
    }
    
    // Uzunluk kontrolü
    if (cleaned.length < 3) {
      return 'Kullanıcı adı en az 3 karakter olmalıdır';
    }
    
    // Boşluk kontrolü
    if (/\s/.test(cleaned)) {
      return 'Kullanıcı adı boşluk içeremez';
    }
    
    // Sadece alfanumerik ve alt çizgi kontrolü 
    if (!/^[a-zA-Z0-9_]+$/.test(cleaned)) {
      return 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir';
    }
    
    // Eğer yukarıdaki kontrollerden geçtiyse kullanılabilirlik kontrolü yap
    try {
      setFormState(prev => ({
        ...prev,
        isCheckingUsername: true
      }));
      
      // authService'den kullanılabilirlik kontrolü
      const result = await authService.checkUsernameAvailability(cleaned);
      setFormState(prev => ({
        ...prev,
        isCheckingUsername: false,
        usernameAvailable: !result.exists
      }));
      
      return result.exists ? 'Bu kullanıcı adı zaten kullanılıyor' : '';
    } catch (error) {
      setFormState(prev => ({
        ...prev,
        isCheckingUsername: false,
        usernameAvailable: null
      }));
      console.error('Username validation error:', error);
      return '';
    }
  }, []);

  // Debounce edilmiş kullanıcı adı kontrolü
  const debouncedCheckUsername = useRef(
    debounce((username: string) => {
      // Sadece değer gerçekten değiştiyse kontrol et
      if (username !== usernameInputRef.current) {
         usernameInputRef.current = username;
         validateUsername(username);
      }
    }, 1100) // 1000ms bekle
  ).current;

  // Input değiştiğinde state'i güncelle ve debounce'u tetikle
  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const isUsernameField = name === 'username';
    
    // @ işaretini kullanıcı adından temizle (kullanıcı yazarken görmesin)
    const processedValue = isUsernameField ? value.replace(/^@/, '') : value;

    setFormState(prev => {
      const newState = { ...prev, [name]: processedValue };
      
      // Anlık alanın validasyon kontrolü
      let fieldError = ''; // Başlangıçta hata yok
      
      // Boş alan kontrolleri (zorunlu alanlar)
      if (name === 'username' && !processedValue.trim()) {
        fieldError = 'Kullanıcı adı gereklidir';
      } 
      else if (name === 'displayName' && !processedValue.trim()) {
        fieldError = 'Ad ve soyad gereklidir';
      }
      else {
        // Format kontrolleri (validateForm'dan)
        fieldError = validateForm({ ...prev, [name]: processedValue }, true)[name];
      }
      
      return {
        ...newState,
        // Güncellenen alan için validasyon hatası
        errors: {
          ...prev.errors,
          [name]: fieldError, // Anlık format validasyonu
          form: undefined
        },
        // Kullanıcı adı değişirse availability sonucunu sıfırla
        usernameAvailable: isUsernameField ? null : prev.usernameAvailable,
      };
    });
    
    if (clearError) clearError();

    // Kullanıcı adı alanıysa ve geçerli uzunluktaysa debounce'u tetikle
    if (isUsernameField) {
       debouncedCheckUsername(processedValue);
    }
  }, [clearError, debouncedCheckUsername]);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      debouncedCheckUsername.cancel();
    };
  }, [debouncedCheckUsername]);

  // Form gönderimi
  const handleSubmit = async (e: FormEvent<HTMLFormElement>, onSuccess?: () => void) => {
    e.preventDefault();
    
    // Format validasyonunu tekrar yap
    const validationErrors = validateForm(formState, true);
    let hasError = Object.keys(validationErrors).length > 0;
    
    // Kullanıcı adı availability kontrolü (state'den)
    const combinedErrors = { ...validationErrors };
    if (formState.usernameAvailable === false) {
      combinedErrors.username = formState.errors.username || 'Bu kullanıcı adı zaten alınmış.';
      hasError = true;
    }
    // Kullanıcı adı henüz kontrol edilmediyse veya kontrol hatası varsa formu gönderme
    else if (formState.username && formState.usernameAvailable === null && !formState.isCheckingUsername) {
        combinedErrors.username = combinedErrors.username || 'Kullanıcı adı kontrol ediliyor/edilemedi.';
        hasError = true;
        // Belki burada tekrar kontrol tetiklenebilir?
        // validateUsername(formState.username);
    }

    if (hasError) {
      setFormState(prev => ({ ...prev, errors: combinedErrors }));
      return;
    }
    
    // Kullanıcı adı kontrolü devam ediyorsa bekle
    if (formState.isCheckingUsername) {
        // Belki bir mesaj gösterilebilir?
        return;
    }

    // Signup işlemi
    setFormState(prev => ({ ...prev, isSubmitting: true, errors: {} }));
    try {
      // Eğer formState.username undefined ise, boş string olarak ayarla
      const username = formState.username || '';
      const displayName = formState.displayName || '';
      
      // authStore.ts'deki signup fonksiyonuna uygun olarak parametreleri güncelliyoruz
      await signup(
        formState.email,
        formState.password,
        username,
        displayName
      );
      setFormState(initialFormState); // Başarılı ise sıfırla
      if (onSuccess) onSuccess();
      
    } catch { // Supabase signup hatası (örn. email zaten var)
      setFormState(prev => ({ 
        ...prev, 
        isSubmitting: false,
        errors: { 
          ...prev.errors,
          form: authError || 'Kayıt başarısız oldu. Lütfen tekrar deneyin.'
        }
      }));
    }
  };
  
  return {
    formState,
    handleChange,
    handleSubmit,
    authError
  };
};