import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { FORM_STYLES } from '../constants/ui';

export const useFormStyles = () => {
  const inputBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const buttonBgColor = useAutoOverlay(5, 'var(--text-color)');
  const buttonTextColor = useAutoOverlay(5, 'var(--bg-color)');
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const viewModeBgColor = useAutoOverlay(2, 'var(--bg-color)');

  const getInputStyles = (hasError?: boolean, isSuccess?: boolean) => {
    let className = FORM_STYLES.input.base;
    
    if (hasError) {
      className += ` ${FORM_STYLES.input.error}`;
    } else if (isSuccess) {
      className += ` ${FORM_STYLES.input.success}`;
    } else {
      className += ` ${FORM_STYLES.input.normal}`;
    }

    const borderColor = hasError 
      ? 'var(--error-color, #ef4444)'
      : isSuccess 
        ? 'var(--success-color, #22c55e)'
        : 'color-mix(in srgb, var(--text-color) 10%, transparent)';

    return {
      className,
      style: {
        backgroundColor: inputBgColor,
        borderColor,
        color: 'var(--text-color)',
        ...(isSuccess && { boxShadow: '0 0 0 1px rgba(34, 197, 94, 0.2)' })
      }
    };
  };

  const getButtonStyles = (variant: 'primary' | 'secondary' = 'primary', disabled = false) => ({
    className: variant === 'primary' ? FORM_STYLES.button.primary : FORM_STYLES.button.secondary,
    style: {
      backgroundColor: variant === 'primary' ? buttonBgColor : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
      color: variant === 'primary' ? buttonTextColor : 'var(--text-color)',
      opacity: disabled ? 0.7 : 1
    }
  });

  return {
    inputBgColor,
    buttonBgColor,
    buttonTextColor,
    toolbarBgColor,
    viewModeBgColor,
    getInputStyles,
    getButtonStyles,
    formSpacing: FORM_STYLES.spacing
  };
};
