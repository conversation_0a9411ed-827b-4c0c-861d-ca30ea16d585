import { useState, ChangeEvent, FormEvent, useCallback } from 'react';
import { IAuthFormState } from '../models/types';
import { useAuth } from './useAuth';
import { useUsernameCheck } from './useUsernameCheck';

// Form başlangıç durumu
const initialFormState: Omit<IAuthFormState, 'username'> = {
  email: '',
  password: '',
  confirmPassword: '',
  displayName: '',
  errors: {},
  isSubmitting: false,
};

// Form doğrulama (username hariç)
const validateForm = (formState: IAuthFormState): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  // Email validasyonu
  if (!formState.email) {
    errors.email = 'Email adresi gereklidir';
  } else if (!/\S+@\S+\.\S+/.test(formState.email)) {
    errors.email = 'Geçerli bir email adresi giriniz';
  }
  
  // Şifre validasyonu
  if (!formState.password) {
    errors.password = '<PERSON><PERSON><PERSON> gereklidir';
  } else if (formState.password.length < 6) {
    errors.password = 'Şifre en az 6 karakter olmalıdır';
  }
  
  // Kayıt olurken şifre kontrolü
  if (!formState.confirmPassword) {
    errors.confirmPassword = 'Şifre onayı gereklidir';
  } else if (formState.password !== formState.confirmPassword) {
    errors.confirmPassword = 'Şifreler eşleşmiyor';
  }
  
  // Görünen isim zorunlu ve validasyonu
  if (!formState.displayName || !formState.displayName.trim()) {
    errors.displayName = 'Ad ve soyad gereklidir';
  } else if (formState.displayName.length < 2) {
    errors.displayName = 'İsim en az 2 karakter olmalıdır';
  }
  
  return errors;
};

/**
 * Kayıt formu için özel hook
 */
export const useNewSignupForm = () => {
  const [formState, setFormState] = useState<Omit<IAuthFormState, 'username'>>(initialFormState);
  const { signup, error: authError, clearError } = useAuth();
  
  // Kullanıcı adı kontrolü için useUsernameCheck hook'unu kullan
  const usernameCheck = useUsernameCheck();
  
  // Input değişikliklerini işle
  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Kullanıcı adı özel işlemi
    if (name === 'username') {
      usernameCheck.handleUsernameChange(e);
      return;
    }
    
    // Diğer alanlar için standart işlem
    setFormState(prev => ({
      ...prev,
      [name]: value,
      errors: {
        ...prev.errors,
        [name]: undefined,
        form: undefined
      }
    }));
    
    if (authError) clearError();
  }, [authError, clearError, usernameCheck]);
  
  // Form gönderimini işle
  const handleSubmit = async (e: FormEvent<HTMLFormElement>, onSuccess?: () => void) => {
    e.preventDefault();
    
    // Form validasyonu (kullanıcı adı hariç)
    const validationErrors = validateForm(formState);
    const hasErrors = Object.keys(validationErrors).length > 0;
    
    // Kullanıcı adı validasyonu
    if (!usernameCheck.isUsernameValid()) {
      validationErrors.username = usernameCheck.usernameError || 'Geçersiz kullanıcı adı';
    }
    
    if (hasErrors || validationErrors.username) {
      setFormState(prev => ({ 
        ...prev, 
        errors: { ...prev.errors, ...validationErrors }
      }));
      return;
    }
    
    // Kullanıcı adı kontrolü devam ediyorsa beklet
    if (usernameCheck.isCheckingUsername) {
      setFormState(prev => ({ 
        ...prev, 
        errors: { 
          ...prev.errors, 
          form: 'Kullanıcı adı kontrolü devam ediyor, lütfen bekleyin.'
        }
      }));
      return;
    }
    
    // Form gönderimi
    setFormState(prev => ({ ...prev, isSubmitting: true, errors: {} }));
    
    try {
      // Kayıt işlemini gerçekleştir
      await signup(
        formState.email,
        formState.password,
        usernameCheck.username,
        formState.displayName
      );
      
      // Başarılı ise form state'ini temizle
      setFormState(initialFormState);
      if (onSuccess) onSuccess();
      
    } catch {
      setFormState(prev => ({ 
        ...prev, 
        isSubmitting: false,
        errors: { 
          ...prev.errors,
          form: authError || 'Kayıt başarısız oldu. Lütfen tekrar deneyin.'
        }
      }));
    }
  };
  
  // Formdaki tüm bilgileri tek bir objede birleştir
  const completeFormState = {
    ...formState,
    username: usernameCheck.username,
    isCheckingUsername: usernameCheck.isCheckingUsername,
    usernameAvailable: usernameCheck.usernameAvailable,
    usernameError: usernameCheck.usernameError
  };
  
  return { 
    formState: completeFormState, 
    handleChange, 
    handleSubmit, 
    authError 
  };
}; 