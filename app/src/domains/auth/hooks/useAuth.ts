import { useEffect, useLayoutEffect } from 'react';
import { useAuthStore } from '../store/authStore';
import { supabase } from '@shared/utils/supabaseClient';
import { useNavigate } from 'react-router-dom';

export const useAuth = () => {
  const { 
    user, 
    profile,
    isLoading, 
    error, 
    isAuthenticated,
    isUsernameSetupRequired,
    login,
    signup,
    logout,
    resetPassword,
    checkAuth,
    clearError,
    signInWithGoogle,
    submitUsername,
    isAuthModalOpen,
    authModalActiveTab,
    openLoginModal,
    openSignupModal,
    closeAuthModal,
    isProfileSheetOpen,
    openProfileSheet,
    closeProfileSheet,
    updateProfileInStore,
    completeUsernameSetup
  } = useAuthStore();

  // Router navigate hook'unu al
  const navigate = useNavigate();

  // Uygulama başlangıcında oturum kontrolü - daha önce çalışması için useLayoutEffect kullanalım
  useLayoutEffect(() => {
    console.log('[useAuth] Immediately checking auth state...');
    // Say<PERSON> yüklendiğinde hemen auth kontrolü yap
    checkAuth();
  }, [checkAuth]);
  
  // Auth state değişikliklerini dinle
  useEffect(() => {
    console.log('[useAuth] Setting up auth state listener...');
    
    // Auth state değişikliklerini dinle
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event) => {
        console.log(`[useAuth] Auth state change: ${event}`);
        
        // Oturum durumu değiştiyse state'i güncelle
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          checkAuth();
        } else if (event === 'SIGNED_OUT') {
          checkAuth();
        }
      }
    );
    
    // Cleanup
    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [checkAuth]);

  // Google girişi sonrası kullanıcı adı kurulumu kontrolü
  useEffect(() => {
    if (isAuthenticated && isUsernameSetupRequired) {
      console.log('[useAuth] Username setup required, redirecting to setup page');
      navigate('/profil-tamamla');
    }
  }, [isAuthenticated, isUsernameSetupRequired, navigate]);

  // Türetilmiş modal durumları
  const isLoginModalOpen = isAuthModalOpen && authModalActiveTab === 'login';
  const isSignupModalOpen = isAuthModalOpen && authModalActiveTab === 'signup';
  
  return {
    // Auth state
    user,
    profile,
    isLoading,
    error,
    isAuthenticated,
    isUsernameSetupRequired,
    
    // Auth actions
    login,
    signup,
    logout,
    resetPassword,
    signInWithGoogle,
    submitUsername,
    updateProfileInStore,
    completeUsernameSetup,
    clearError,
    
    // Modal states and actions
    isAuthModalOpen,
    authModalActiveTab,
    isLoginModalOpen,
    isSignupModalOpen,
    openLoginModal,
    openSignupModal,
    closeAuthModal,
    
    // Profile sheet
    isProfileSheetOpen,
    openProfileSheet,
    closeProfileSheet
  };
};