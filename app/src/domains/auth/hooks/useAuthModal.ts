import { useEffect, RefObject } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface UseAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  modalRef: RefObject<HTMLDivElement>;
}

export const useAuthModal = ({
  isOpen,
  onClose,
  modalRef
}: UseAuthModalProps) => {

  // Click outside to close
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, modalRef]);

  // Get modal styles
  const getModalStyles = () => {
    const navBgColor = useAutoOverlay(8, 'var(--bg-color)');

    return {
      backgroundColor: navBgColor,
      borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
    };
  };

  return {
    getModalStyles
  };
};
