import { FC, useState, useCallback } from 'react';
import { useLoginForm } from '../../hooks/useAuthForm';
import { Loader2, ArrowLeft } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import GoogleIcon from '@shared/components/Icons/GoogleIcon';
import { supabase } from '@shared/utils/supabaseClient';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface LoginFormProps {
  onClose: () => void;
}

export const LoginForm: FC<LoginFormProps> = ({ onClose }) => {
  const [isResetMode, setIsResetMode] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResetting, setIsResetting] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [resetError, setResetError] = useState<string | null>(null);

  const { formState, handleChange, handleSubmit, authError } = useLoginForm();
  const { resetPassword } = useAuth();

  const inputBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const buttonBgColor = useAutoOverlay(5, 'var(--text-color)');
  const buttonTextColor = useAutoOverlay(5, 'var(--bg-color)');

  // Google ile giriş (basitleştirilmiş)
  const handleGoogleSignIn = useCallback(async () => {
    try {
      await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        }
      });
      // Bu fonksiyon redirect yapar, sonuç beklemiyoruz
    } catch (error) {
      console.error('Google girişi sırasında hata:', error);
    }
  }, []);

  // Şifre sıfırlama işlemi
  const handleResetPassword = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!resetEmail.trim()) return;

    setIsResetting(true);
    setResetError(null);

    try {
      await resetPassword(resetEmail);
      setResetSuccess(true);
    } catch (error) {
      setResetError(error instanceof Error ? error.message : 'Şifre sıfırlama işlemi başarısız oldu');
    } finally {
      setIsResetting(false);
    }
  }, [resetEmail, resetPassword]);

  // Giriş formuna geri dön
  const backToLogin = useCallback(() => {
    setIsResetMode(false);
    setResetEmail('');
    setResetSuccess(false);
    setResetError(null);
  }, []);

  // Şifre sıfırlama formu
  if (isResetMode) {
    return (
      <form onSubmit={handleResetPassword} className="space-y-3.5">
        <div className="flex items-center mb-2">
          <button
            type="button"
            onClick={backToLogin}
            className="mr-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            style={{ color: 'var(--text-color)' }}
          >
            <ArrowLeft size={18} />
          </button>
          <h2 className="text-lg font-medium" style={{ color: 'var(--text-color)' }}>
            Şifre Sıfırlama
          </h2>
        </div>

        {resetSuccess ? (
          <div className="bg-green-500/10 border border-green-500/20 text-green-600 dark:text-green-400 px-4 py-3 rounded-xl text-sm">
            Şifre sıfırlama bağlantısı e-posta adresinize gönderildi. Lütfen e-postanızı kontrol edin.
          </div>
        ) : (
          <>
            {resetError && (
              <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
                {resetError}
              </div>
            )}

            <div className="text-sm mb-4" style={{ color: 'var(--text-color)' }}>
              Şifrenizi sıfırlamak için kayıtlı e-posta adresinizi girin. Size bir sıfırlama bağlantısı göndereceğiz.
            </div>

            <div className="space-y-1.5">
              <label
                htmlFor="resetEmail"
                className="block text-sm font-medium"
                style={{ color: 'var(--text-color)' }}
              >
                E-posta
              </label>
              <div className="relative">
                <input
                  id="resetEmail"
                  name="resetEmail"
                  type="email"
                  autoComplete="email"
                  required
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border focus:ring-blue-500/30"
                  style={{
                    backgroundColor: inputBgColor,
                    borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                    color: 'var(--text-color)'
                  }}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="w-full py-3 px-4 rounded-xl font-medium transition-opacity"
                style={{
                  backgroundColor: buttonBgColor,
                  color: buttonTextColor,
                  opacity: isResetting ? 0.7 : 1
                }}
                disabled={isResetting}
              >
                {isResetting ? (
                  <span className="flex items-center justify-center" style={{ color: buttonTextColor }}>
                    <Loader2 size={18} className="animate-spin mr-2" />
                    İşleniyor...
                  </span>
                ) : (
                  'Şifre Sıfırlama Bağlantısı Gönder'
                )}
              </button>
            </div>
          </>
        )}
      </form>
    );
  }

  // Normal giriş formu
  return (
    <form onSubmit={(e) => handleSubmit(e, onClose)} className="space-y-3.5">
      {/* Form hatası */}
      {(formState.errors.form || authError) && (
        <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
          {formState.errors.form || authError}
        </div>
      )}

      {/* Email alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="email"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          E-posta
        </label>
        <div className="relative">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formState.email}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              formState.errors.email ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: formState.errors.email ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {formState.errors.email && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{formState.errors.email}</p>
        )}
      </div>

      {/* Şifre alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="password"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          Şifre
        </label>
        <div className="relative">
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={formState.password}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              formState.errors.password ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: formState.errors.password ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {formState.errors.password && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{formState.errors.password}</p>
        )}
      </div>

      <div className="flex items-center justify-end">
        <button
          type="button"
          onClick={() => setIsResetMode(true)}
          className="text-sm font-medium hover:underline"
          style={{ color: 'var(--text-color)', opacity: 0.8 }}
        >
          Şifremi unuttum
        </button>
      </div>

      <div>
        <button
          type="submit"
          className="w-full py-3 px-4 rounded-xl font-medium transition-opacity"
          style={{
            backgroundColor: buttonBgColor,
            color: buttonTextColor,
            opacity: formState.isSubmitting ? 0.7 : 1
          }}
          disabled={formState.isSubmitting}
        >
          {formState.isSubmitting ? (
            <span className="flex items-center justify-center" style={{ color: buttonTextColor }}>
              <Loader2 size={18} className="animate-spin mr-2" />
              Giriş Yapılıyor...
            </span>
          ) : (
            'Giriş Yap'
          )}
        </button>
      </div>

      {/* Google ile giriş butonu */}
      <div className="my-4 flex items-center">
        <div className="flex-1 h-px" style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}></div>
        <span className="px-2 text-sm" style={{ color: 'color-mix(in srgb, var(--text-color) 60%, transparent)' }}>veya</span>
        <div className="flex-1 h-px" style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}></div>
      </div>

      <button
        type="button"
        onClick={handleGoogleSignIn}
        disabled={formState.isSubmitting}
        className="w-full py-3 px-4 rounded-xl flex items-center justify-center gap-2 transition-all"
        style={{
          backgroundColor: 'color-mix(in srgb, var(--text-color) 5%, transparent)',
          border: '1px solid',
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)',
          color: 'var(--text-color)'
        }}
      >
        <GoogleIcon size={20} />
        <span>Google ile Devam Et</span>
      </button>
    </form>
  );
};