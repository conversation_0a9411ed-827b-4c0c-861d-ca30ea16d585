import { FC, useRef } from 'react';
import { LoginForm } from './LoginForm';
import { SignupForm } from './SignupForm';
import { X as CloseIcon } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useAuthModal } from '../../hooks';
import { autoHue } from '@shared/hooks/autohue';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

export const AuthSheet: FC = () => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const {
    isAuthModalOpen,
    closeAuthModal,
    authModalActiveTab,
    openLoginModal,
    openSignupModal,
  } = useAuth();

  const { getModalStyles } = useAuthModal({
    isOpen: isAuthModalOpen,
    onClose: closeAuthModal,
    modalRef: sheetRef
  });

  const modalStyles = getModalStyles();
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const activeStyle = autoHue('var(--text-color)');
  const defaultStyle = { color: 'var(--text-color)' };

  // Click outside handling is now managed by useAuthModal hook

  if (!isAuthModalOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isAuthModalOpen ? 1 : 0 }}
        onClick={closeAuthModal}
      />

      {/* Sheet */}
      <div
        ref={sheetRef}
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[85vh] md:top-[12%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[430px] md:max-h-[75vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{
          backgroundColor: modalStyles.backgroundColor,
          borderColor: modalStyles.borderColor
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Header with close button and segment control side by side */}
        <div className="mx-4 mt-4 flex items-center justify-between">
          {/* Segment Control - Header ile aynı satırda */}
          <div className="flex-1 flex items-center p-1 rounded-xl overflow-hidden" style={{
            backgroundColor: toolbarBgColor
          }}>
            <button
              onClick={openLoginModal}
              className={`flex-1 px-3 py-2 rounded-xl text-base font-medium transition-colors ${
                authModalActiveTab === 'login' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={authModalActiveTab === 'login' ? activeStyle : defaultStyle}
            >
              Giriş Yap
            </button>
            <div
              className="w-px h-5 mx-1"
              style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 30%, transparent)' }}
            />
            <button
              onClick={openSignupModal}
              className={`flex-1 px-3 py-2 rounded-xl text-base font-medium transition-colors ${
                authModalActiveTab === 'signup' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={authModalActiveTab === 'signup' ? activeStyle : defaultStyle}
            >
              Kayıt Ol
            </button>
          </div>

          {/* Close Button */}
          <button
            onClick={closeAuthModal}
            className="w-8 h-8 flex items-center justify-center ml-3 rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <CloseIcon size={18} />
          </button>
        </div>

        {/* Sabit üst boşluk */}
        <div className="py-2"></div>

        {/* Form Content - Üst padding kaldırıldı, alt padding kaldırıldı, max-h orijinaline yakın */}
        <div className="px-6 pt-0 pb-0 overflow-y-auto max-h-[calc(85vh-6rem)] md:max-h-[calc(75vh-6rem)]">
          {authModalActiveTab === 'login' ? (
            <LoginForm onClose={closeAuthModal} />
          ) : (
            <SignupForm onClose={closeAuthModal} />
          )}
        </div>
      </div>
    </>
  );
};