import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../store/authStore';
import { X, Loader2, Check, AlertCircle, Pencil, Save } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useUsernameCheck } from '../../hooks/useUsernameCheck';

export const ProfileSheet: React.FC = () => {
  const {
    profile,
    user,
    isLoading,
    error,
    logout,
    updateProfileInStore,
    closeProfileSheet,
    isProfileSheetOpen,
  } = useAuthStore();

  // Kullanıcı adı kontrolü için hook'u kullan
  const {
    username,
    handleUsernameChange,
    isCheckingUsername, 
    usernameAvailable,
    usernameError,
    isUsernameValid,
    setUsername
  } = useUsernameCheck(
    // Başlangıç değeri (@ olmadan)
    profile?.username ? profile.username.replace(/^@/, '') : '',
    // Orjinal değer (@ olmadan)
    profile?.username ? profile.username.replace(/^@/, '') : ''
  );

  const [currentDisplayName, setCurrentDisplayName] = useState(profile?.displayName || '');
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Düzenleme modları için state ekleyelim
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [isEditingDisplayName, setIsEditingDisplayName] = useState(false);
  
  // Stil değişkenleri
  const navBgColor = autoOverlay ? useAutoOverlay(8, 'var(--bg-color)') : '#1e1e1e';
  const inputBgColor = autoOverlay ? useAutoOverlay(4, 'var(--bg-color)') : '';
  const buttonBgColor = autoOverlay ? useAutoOverlay(5, 'var(--text-color)') : 'var(--text-color)';
  const buttonTextColor = autoOverlay ? useAutoOverlay(5, 'var(--bg-color)') : 'var(--bg-color)';
  const viewModeBgColor = autoOverlay ? useAutoOverlay(2, 'var(--bg-color)') : 'rgba(0,0,0,0.03)';

  useEffect(() => {
    if (profile) {
      setCurrentDisplayName(profile.displayName || '');
      // Profil değiştiğinde düzenleme modlarını sıfırla
      setIsEditingUsername(false);
      setIsEditingDisplayName(false);
    }
  }, [profile]);

  const handleDisplayNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentDisplayName(e.target.value);
  };

  // Değişiklikleri kaydet
  const handleSaveChanges = async () => {
    // Düzenleme modundan çık
    setIsEditingUsername(false);
    setIsEditingDisplayName(false);
    
    setFormError(null);
    
    // Form doğrulama
    if (!currentDisplayName.trim()) {
      setFormError('Görünen ad boş bırakılamaz.');
      return;
    }
    
    // Kullanıcı adı geçerli mi?
    if (!isUsernameValid()) {
      setFormError(usernameError || 'Geçersiz kullanıcı adı.');
      return;
    }
    
    // Kullanıcı adı kontrolü hala devam ediyorsa bekle
    if (isCheckingUsername) {
      setFormError('Kullanıcı adı kontrolü devam ediyor, lütfen bekleyin.');
      return;
    }
    
    setIsSubmitting(true);
    try {
      await updateProfileInStore({
        displayName: currentDisplayName,
        username: username, // @ olmadan gönder
      });
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Profil güncellenirken bir hata oluştu.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    closeProfileSheet(); // Çıkış yapınca profili de kapat
  };

  // Düzenleme iptali
  const handleCancelEditing = () => {
    // Profil bilgilerini orijinal değerlerine geri döndür
    if (profile) {
      setCurrentDisplayName(profile.displayName || '');
      setUsername(profile.username ? profile.username.replace(/^@/, '') : '');
    }
    // Düzenleme modlarını kapat
    setIsEditingUsername(false);
    setIsEditingDisplayName(false);
    // Hataları temizle
    setFormError(null);
  };

  if (!isProfileSheetOpen || !user) {
    return null; // Modal kapalıysa veya kullanıcı yoksa hiçbir şey gösterme
  }

  const overallError = formError || error; // Hem formun kendi hatasını hem de store'dan gelen genel hatayı göster
  const isEditing = isEditingUsername || isEditingDisplayName;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: 1 }}
        onClick={isEditing ? undefined : closeProfileSheet}
      />
      
      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />
        
        {/* Header with title and close button */}
        <div className="mx-4 mt-3 mb-2 flex items-center justify-between">
          <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
            Profilim
          </h3>
          <button
            onClick={isEditing ? handleCancelEditing : closeProfileSheet}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
            aria-label={isEditing ? "Düzenleme iptal" : "Kapat"}
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pt-3 pb-6 overflow-y-auto max-h-[calc(80vh-6rem)] md:max-h-[calc(70vh-6rem)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-[var(--text-color)] opacity-70" />
            </div>
          ) : (
            <div className="space-y-3.5">
              {/* Form hatası */}
              {overallError && (
                <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
                  {overallError}
                </div>
              )}
              
              {/* Email alanı (her zaman salt okunur) */}
              <div className="space-y-1.5">
                <label 
                  htmlFor="email" 
                  className="block text-sm font-medium"
                  style={{ color: 'var(--text-color)' }}
                >
                  E-posta
                </label>
                <div className="relative">
                  <input
                    id="email"
                    type="email"
                    value={user.email || ''}
                    disabled
                    className="w-full px-4 py-3 rounded-xl text-sm focus:outline-none border"
                    style={{ 
                      backgroundColor: 'color-mix(in srgb, var(--text-color) 5%, transparent)',
                      borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                      color: 'var(--text-color)',
                      opacity: 0.7
                    }}
                  />
                </div>
              </div>
              
              {/* Görünen Ad alanı - düzenleme modu */}
              <div className="space-y-1.5">
                <div className="flex items-center justify-between">
                  <label 
                    htmlFor="displayNameProfile" 
                    className="block text-sm font-medium"
                    style={{ color: 'var(--text-color)' }}
                  >
                    Görünen Ad
                  </label>
                  
                  {/* Düzenleme/Kaydet butonu */}
                  {!isEditingDisplayName ? (
                    <button 
                      type="button"
                      onClick={() => setIsEditingDisplayName(true)}
                      className="p-1.5 rounded-md hover:bg-[var(--text-color)]/5 transition-colors"
                      aria-label="Görünen adı düzenle"
                    >
                      <Pencil size={14} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
                    </button>
                  ) : (
                    <button 
                      type="button"
                      onClick={handleSaveChanges}
                      className="p-1.5 rounded-md hover:bg-[var(--text-color)]/5 transition-colors"
                      aria-label="Değişiklikleri kaydet"
                    >
                      <Save size={14} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
                    </button>
                  )}
                </div>
                
                <div className="relative">
                  {isEditingDisplayName ? (
                    /* Düzenleme modu */
                    <input
                      id="displayNameProfile"
                      type="text"
                      value={currentDisplayName}
                      onChange={handleDisplayNameChange}
                      className="w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border focus:ring-blue-500/30"
                      style={{ 
                        backgroundColor: inputBgColor,
                        borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                        color: 'var(--text-color)'
                      }}
                      autoFocus
                    />
                  ) : (
                    /* Görüntüleme modu */
                    <div 
                      className="w-full px-4 py-3 rounded-xl text-sm border"
                      style={{ 
                        backgroundColor: viewModeBgColor,
                        borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                        color: 'var(--text-color)'
                      }}
                    >
                      {currentDisplayName || <span className="opacity-50">Görünen ad belirlenmemiş</span>}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Kullanıcı Adı alanı */}
              <div className="space-y-1.5">
                <div className="flex items-center justify-between">
                  <label 
                    htmlFor="usernameProfile" 
                    className="block text-sm font-medium"
                    style={{ color: 'var(--text-color)' }}
                  >
                    Kullanıcı Adı
                  </label>
                  
                  {/* Düzenleme/Kaydet butonu */}
                  {!isEditingUsername ? (
                    <button 
                      type="button"
                      onClick={() => setIsEditingUsername(true)}
                      className="p-1.5 rounded-md hover:bg-[var(--text-color)]/5 transition-colors"
                      aria-label="Kullanıcı adını düzenle"
                    >
                      <Pencil size={14} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
                    </button>
                  ) : (
                    <button 
                      type="button"
                      onClick={handleSaveChanges}
                      className="p-1.5 rounded-md hover:bg-[var(--text-color)]/5 transition-colors"
                      aria-label="Değişiklikleri kaydet"
                    >
                      <Save size={14} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
                    </button>
                  )}
                </div>
                
                <div className="relative">
                  {isEditingUsername ? (
                    /* Düzenleme modu */
                    <>
                      <div 
                        className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                        style={{ color: 'var(--text-color)' }}
                      >
                        @
                      </div>
                      <input
                        id="usernameProfile"
                        type="text"
                        value={username}
                        onChange={handleUsernameChange}
                        className="w-full pl-8 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border focus:ring-blue-500/30"
                        style={{ 
                          backgroundColor: inputBgColor,
                          borderColor: usernameError
                            ? 'var(--error-color, #ef4444)'
                            : usernameAvailable === true
                              ? 'var(--success-color, #22c55e)'
                              : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                          color: 'var(--text-color)'
                        }}
                        pattern="^[a-zA-Z0-9_]+$"
                        title="Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir"
                        autoFocus
                      />
                      
                      {/* Kullanıcı adı kontrol durumunu göster */}
                      {isCheckingUsername && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <Loader2 size={16} className="animate-spin text-[var(--text-color)] opacity-60" />
                        </div>
                      )}
                      
                      {/* Kullanıcı adı kullanılabilir */}
                      {!isCheckingUsername && usernameAvailable === true && username.length >= 4 && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <Check size={16} className="text-green-500" />
                        </div>
                      )}
                      
                      {/* Kullanıcı adı kullanılamaz */}
                      {!isCheckingUsername && usernameAvailable === false && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <AlertCircle size={16} className="text-red-500" />
                        </div>
                      )}
                    </>
                  ) : (
                    /* Görüntüleme modu */
                    <div 
                      className="w-full px-4 py-3 rounded-xl text-sm border"
                      style={{ 
                        backgroundColor: viewModeBgColor,
                        borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                        color: 'var(--text-color)'
                      }}
                    >
                      {profile?.username || <span className="opacity-50">Kullanıcı adı belirlenmemiş</span>}
                    </div>
                  )}
                </div>
                
                {/* Kullanıcı adı hata mesajı (sadece düzenleme modunda) */}
                {isEditingUsername && usernameError && (
                  <p className="text-xs text-red-500 mt-1">
                    {usernameError}
                  </p>
                )}
                
                {/* Yardım metni (sadece düzenleme modunda) */}
                {isEditingUsername && (
                  <p className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
                    <span className="flex items-start">
                      <span className="mr-1">•</span>
                      <span>Kullanıcı adınız sadece harf, rakam ve alt çizgi (_) içerebilir</span>
                    </span>
                  </p>
                )}
              </div>
              
              {/* Düzenleme açıksa kaydet/iptal butonları göster */}
              {isEditing && (
                <div className="pt-2">
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={handleCancelEditing}
                      className="w-1/2 py-2 px-4 rounded-xl font-medium transition-opacity"
                      style={{
                        backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                        color: 'var(--text-color)',
                      }}
                      disabled={isSubmitting}
                    >
                      İptal
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleSaveChanges}
                      className="w-1/2 py-2 px-4 rounded-xl font-medium transition-opacity"
                      style={{
                        backgroundColor: buttonBgColor,
                        color: buttonTextColor,
                        opacity: isSubmitting ? 0.7 : 1
                      }}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center">
                          <Loader2 size={18} className="animate-spin mr-2" />
                          Kaydediliyor...
                        </span>
                      ) : (
                        'Kaydet'
                      )}
                    </button>
                  </div>
                </div>
              )}
              
              {/* Çıkış butonu (düzenleme yoksa göster) */}
              {!isEditing && (
                <div className="pt-2">
                  <button
                    type="button"
                    onClick={handleLogout}
                    className="w-full py-3 px-4 rounded-xl font-medium transition-opacity mt-2"
                    style={{
                      backgroundColor: 'color-mix(in srgb, var(--error-color, #ef4444) 80%, transparent)',
                      color: 'white',
                    }}
                  >
                    Çıkış Yap
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProfileSheet; 