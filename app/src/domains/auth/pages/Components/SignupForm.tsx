import { FC } from 'react';
import { useNewSignupForm } from '../../hooks/useSignupForm';
import { Loader2, Check, X } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface SignupFormProps {
  onClose: () => void;
}

export const SignupForm: FC<SignupFormProps> = ({ onClose }) => {
  const { formState, handleChange, handleSubmit, authError } = useNewSignupForm();

  const {
    email, password, confirmPassword, username, displayName,
    errors, isSubmitting, isCheckingUsername, usernameAvailable, usernameError
  } = formState;

  // Username durumuna göre stil ve ikonlar
  let usernameBorderColor = 'color-mix(in srgb, var(--text-color) 10%, transparent)';
  let usernameIcon = null;

  if (errors.username || usernameError) {
    usernameBorderColor = 'var(--error-color, #ef4444)';
    usernameIcon = <X size={16} className="text-red-500" />;
  } else if (isCheckingUsername) {
    usernameBorderColor = 'color-mix(in srgb, var(--text-color) 30%, transparent)';
    usernameIcon = <Loader2 size={16} className="animate-spin text-blue-500" />;
  } else if (usernameAvailable === true) {
    usernameBorderColor = 'var(--success-color, #22c55e)';
    usernameIcon = <Check size={18} className="text-green-500 font-bold" />;
  } else if (usernameAvailable === false) {
    usernameBorderColor = 'var(--error-color, #ef4444)';
    usernameIcon = <X size={16} className="text-red-500" />;
  }

  // Get styles from hook (keeping old approach for now)
  const inputBgColor = autoOverlay ? useAutoOverlay(4, 'var(--bg-color)') : '';
  const buttonBgColor = autoOverlay ? useAutoOverlay(5, 'var(--text-color)') : 'var(--text-color)';
  const buttonTextColor = autoOverlay ? useAutoOverlay(5, 'var(--bg-color)') : 'var(--bg-color)';

  return (
    <form onSubmit={(e) => handleSubmit(e, onClose)} className="space-y-3">
      {/* Form hatası */}
      {(errors.form || authError) && (
        <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
          {errors.form || authError}
        </div>
      )}

      {/* Email alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="email"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          E-posta
        </label>
        <div className="relative">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              errors.email ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: errors.email ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {errors.email && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{errors.email}</p>
        )}
      </div>

      {/* İsim-Soyisim alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="displayName"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          Adınız Soyadınız
        </label>
        <div className="relative">
          <input
            id="displayName"
            name="displayName"
            type="text"
            autoComplete="name"
            required
            value={displayName || ''}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              errors.displayName ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: errors.displayName ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {errors.displayName && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{errors.displayName}</p>
        )}
      </div>

      {/* Kullanıcı adı alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="username"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          Kullanıcı Adı
        </label>
        <div className="relative">
          <div
            className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
            style={{ color: 'var(--text-color)' }}
          >
            @
          </div>
          <input
            id="username"
            name="username"
            type="text"
            autoComplete="username"
            required
            value={username || ''}
            onChange={handleChange}
            className={`w-full pl-8 pr-10 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border transition-all duration-200 ${
              errors.username || usernameError
                ? 'focus:ring-red-500/30 border-red-500/50'
                : usernameAvailable === true
                  ? 'focus:ring-green-500/30 border-green-500/70 border-2'
                  : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: usernameBorderColor,
              color: 'var(--text-color)',
              boxShadow: usernameAvailable === true ? '0 0 0 1px rgba(34, 197, 94, 0.2)' : 'none'
            }}
            placeholder="kullaniciadi"
            disabled={isCheckingUsername}
            aria-invalid={!!errors.username || !!usernameError || usernameAvailable === false}
            aria-describedby={errors.username || usernameError ? "username-error" : undefined}
            pattern="^@?[a-zA-Z0-9_]+$"
            title="Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir"
          />
          {usernameIcon && (
            <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
              {usernameIcon}
            </div>
          )}
        </div>
        <p className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
          <span className="flex items-start">
            <span className="mr-1">•</span>
            <span>Kullanıcı adınız <span className="font-medium">sadece harf, rakam ve alt çizgi (_) içerebilir</span></span>
          </span>
        </p>
        {(errors.username || usernameError) && (
          <p id="username-error" className="text-red-600 dark:text-red-400 text-xs mt-1">
            {errors.username || usernameError}
          </p>
        )}
        {/* Kullanıcı adı geçerli olduğunda başarılı mesajı */}
        {!errors.username && !usernameError && usernameAvailable === true && username && (
          <p className="text-green-600 dark:text-green-400 text-xs mt-1">
            <span className="flex items-center">
              <Check size={12} className="mr-1" /> Bu kullanıcı adı kullanılabilir
            </span>
          </p>
        )}
        {/* Kullanıcı adı kontrol edilirken bekliyor mesajı */}
        {!errors.username && !usernameError && isCheckingUsername && username && (
          <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">
            <span className="flex items-center">
              <Loader2 size={12} className="animate-spin mr-1" /> Kullanıcı adı kontrol ediliyor...
            </span>
          </p>
        )}
      </div>

      {/* Şifre alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="password"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          Şifre
        </label>
        <div className="relative">
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            value={password}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              errors.password ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: errors.password ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {errors.password && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{errors.password}</p>
        )}
      </div>

      {/* Şifre tekrarı alanı */}
      <div className="space-y-1.5">
        <label
          htmlFor="confirmPassword"
          className="block text-sm font-medium"
          style={{ color: 'var(--text-color)' }}
        >
          Şifre Tekrarı
        </label>
        <div className="relative">
          <input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            autoComplete="new-password"
            required
            value={confirmPassword}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
              errors.confirmPassword ? 'focus:ring-red-500/30' : 'focus:ring-blue-500/30'
            }`}
            style={{
              backgroundColor: inputBgColor,
              borderColor: errors.confirmPassword ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
              color: 'var(--text-color)'
            }}
          />
        </div>
        {errors.confirmPassword && (
          <p className="text-red-600 dark:text-red-400 text-xs mt-1">{errors.confirmPassword}</p>
        )}
      </div>

      {/* Kullanım koşulları onayı */}
      <div className="flex items-start mt-4">
        <div className="flex items-center h-5">
          <input
            id="terms"
            name="terms"
            type="checkbox"
            required
            className="w-4 h-4 rounded border focus:ring-2 focus:ring-offset-0 focus:outline-none"
            style={{
              accentColor: 'var(--text-color)',
              borderColor: 'color-mix(in srgb, var(--text-color) 30%, transparent)',
              backgroundColor: 'transparent',
              color: 'var(--text-color)',
            }}
          />
        </div>
        <div className="ml-3 text-sm">
          <label htmlFor="terms" style={{ color: 'var(--text-color)' }}>
            <span>Kullanım koşullarını ve gizlilik politikasını kabul ediyorum</span>
          </label>
        </div>
      </div>

      {/* Kayıt butonu - Üstteki pt-3 kaldırıldı */}
      <div>
        <button
          type="submit"
          className="w-full py-3 px-4 rounded-xl font-medium transition-opacity disabled:opacity-70"
          style={{
            backgroundColor: buttonBgColor,
            color: buttonTextColor,
          }}
          disabled={isSubmitting || isCheckingUsername || usernameAvailable === false}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center" style={{ color: buttonTextColor }}>
              <Loader2 size={18} className="animate-spin mr-2" />
              Kayıt yapılıyor...
            </span>
          ) : (
            'Kayıt Ol'
          )}
        </button>
      </div>
    </form>
  );
};