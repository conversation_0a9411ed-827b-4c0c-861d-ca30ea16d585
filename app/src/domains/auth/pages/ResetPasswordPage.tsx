import { FC, useState, useEffect, useCallback } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { Loader2 } from 'lucide-react';
import { supabase } from '@shared/utils/supabaseClient';
import { useNavigate } from 'react-router-dom';

const ResetPasswordPage: FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isValidLink, setIsValidLink] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  
  const navigate = useNavigate();
  
  const inputBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const buttonBgColor = useAutoOverlay(5, 'var(--text-color)');
  const buttonTextColor = useAutoOverlay(5, 'var(--bg-color)');
  
  // URL parametrelerini kontrol et
  useEffect(() => {
    const checkResetLink = async () => {
      // URL'den token hash'ini al
      const fragment = window.location.hash;
      const hasAccessToken = fragment.includes('access_token=');
      const hasType = fragment.includes('type=recovery');
      
      if (hasAccessToken && hasType) {
        setIsValidLink(true);
      } else {
        setError('Geçersiz veya süresi dolmuş şifre sıfırlama bağlantısı.');
      }
      
      setIsChecking(false);
    };
    
    checkResetLink();
  }, []);
  
  const handleResetPassword = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Doğrulama
    if (password.length < 6) {
      setError('Şifre en az 6 karakter olmalıdır.');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Şifreler eşleşmiyor.');
      return;
    }
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // Supabase'in şifre güncelleme metodunu çağır
      const { error } = await supabase.auth.updateUser({
        password: password
      });
      
      if (error) throw error;
      
      setSuccess(true);
      
      // 3 saniye sonra ana sayfaya yönlendir
      setTimeout(() => {
        navigate('/');
      }, 3000);
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Şifre güncellenirken bir hata oluştu.');
      }
    } finally {
      setIsProcessing(false);
    }
  }, [password, confirmPassword, navigate]);
  
  if (isChecking) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md p-6 rounded-2xl shadow-lg">
          <div className="flex justify-center">
            <Loader2 size={24} className="animate-spin" style={{ color: 'var(--text-color)' }} />
          </div>
          <p className="text-center mt-4" style={{ color: 'var(--text-color)' }}>Bağlantı kontrol ediliyor...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md p-6 rounded-2xl shadow-lg" style={{ backgroundColor: 'var(--bg-color)' }}>
        <h1 className="text-xl font-bold mb-6 text-center" style={{ color: 'var(--text-color)' }}>
          Şifre Sıfırlama
        </h1>
        
        {!isValidLink ? (
          <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
            {error}
            <div className="mt-4">
              <button
                onClick={() => navigate('/')}
                className="w-full py-3 px-4 rounded-xl font-medium transition-opacity"
                style={{
                  backgroundColor: buttonBgColor,
                  color: buttonTextColor
                }}
              >
                Ana Sayfaya Dön
              </button>
            </div>
          </div>
        ) : success ? (
          <div className="bg-green-500/10 border border-green-500/20 text-green-600 dark:text-green-400 px-4 py-3 rounded-xl text-sm">
            Şifreniz başarıyla güncellenmiştir. Giriş sayfasına yönlendiriliyorsunuz...
          </div>
        ) : (
          <form onSubmit={handleResetPassword} className="space-y-4">
            {error && (
              <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
                {error}
              </div>
            )}
            
            <div className="space-y-1.5">
              <label htmlFor="password" className="block text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                Yeni Şifre
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border focus:ring-blue-500/30"
                style={{ 
                  backgroundColor: inputBgColor,
                  borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                  color: 'var(--text-color)'
                }}
                required
                minLength={6}
              />
              <p className="text-xs mt-1" style={{ color: 'var(--text-color-light, #666)' }}>
                Şifre en az 6 karakter olmalıdır.
              </p>
            </div>
            
            <div className="space-y-1.5">
              <label htmlFor="confirmPassword" className="block text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                Şifreyi Onayla
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border focus:ring-blue-500/30"
                style={{ 
                  backgroundColor: inputBgColor,
                  borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                  color: 'var(--text-color)'
                }}
                required
              />
            </div>
            
            <div className="pt-2">
              <button
                type="submit"
                className="w-full py-3 px-4 rounded-xl font-medium transition-opacity"
                style={{
                  backgroundColor: buttonBgColor,
                  color: buttonTextColor,
                  opacity: isProcessing ? 0.7 : 1
                }}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <span className="flex items-center justify-center">
                    <Loader2 size={18} className="animate-spin mr-2" />
                    İşleniyor...
                  </span>
                ) : (
                  'Şifreyi Güncelle'
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ResetPasswordPage; 