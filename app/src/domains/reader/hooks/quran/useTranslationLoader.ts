import { useState, useEffect, useRef, useMemo } from 'react';
import { VerseTranslationsMap, ITranslator } from '@reader/models/types'; 
import { ContentMode } from './useContentMode';
import { fetchDataFromR2 } from '@shared/utils/r2DataFetcher';

// Type definitions
interface ITranslationItem {
  verse_no: number;
  text: string;
  footnote?: string;
}
type TranslatorSurahData = ITranslationItem[];

// Cache yapılandırması
const CACHE_VERSION = 'v1';
const CACHE_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 1 hafta (milisaniye)
const TRANSLATORS_CACHE_KEY = `quran_translators_${CACHE_VERSION}`;

interface CacheMetadata {
  timestamp: number;
  version: string;
}

interface CachedData<T> {
  data: T;
  metadata: CacheMetadata;
}

// Cache Yardımcı Fonksiyonlar
function getTranslationCacheKey(translatorId: string, surahId: number): string {
  return `quran_translation_${CACHE_VERSION}_${translatorId}_${surahId}`;
}

function saveToCache<T>(key: string, data: T): void {
  try {
    const cachedData: CachedData<T> = {
      data,
      metadata: {
        timestamp: Date.now(),
        version: CACHE_VERSION
      }
    };
    
    localStorage.setItem(key, JSON.stringify(cachedData));
    console.log(`[TranslationCache] Saved data to ${key}`);
  } catch (error) {
    console.error(`[TranslationCache] Error saving to cache for ${key}:`, error);
    // localStorage doluysa, cache temizlenmeyi deneyebilir
    try {
      localStorage.removeItem(key);
    } catch {
      // Sessizce görmezden gel
    }
  }
}

function getFromCache<T>(key: string): T | null {
  try {
    const cachedItem = localStorage.getItem(key);
    if (!cachedItem) return null;
    
    const parsed = JSON.parse(cachedItem) as CachedData<T>;
    
    // Cache versiyonu kontrol et
    if (parsed.metadata.version !== CACHE_VERSION) {
      console.log(`[TranslationCache] Outdated cache version for ${key}, clearing`);
      localStorage.removeItem(key);
      return null;
    }
    
    // Son kullanma tarihini kontrol et
    const now = Date.now();
    if (now - parsed.metadata.timestamp > CACHE_EXPIRY_TIME) {
      console.log(`[TranslationCache] Expired cache for ${key}, clearing`);
      localStorage.removeItem(key);
      return null;
    }
    
    console.log(`[TranslationCache] Hit for ${key}`);
    return parsed.data;
  } catch (error) {
    console.error(`[TranslationCache] Error reading from cache for ${key}:`, error);
    return null;
  }
}

// Return type for the hook
export interface UseTranslationLoaderReturn {
  translations: VerseTranslationsMap;
  loading: boolean;
  error: Error | null;
  availableTranslators: ITranslator[];
  translationsMap: Map<number, Map<string, {
    paragraphs: string[];
    footnotes: string[];
  }>>;
}

// Main hook implementation
export function useTranslationLoader(
  surahId: number | null,
  contentMode: ContentMode,
  selectedTranslators: string[] = []
): UseTranslationLoaderReturn {
  // State definitions
  const [translationsMap, setTranslationsMap] = useState<Map<number, Map<string, { paragraphs: string[]; footnotes: string[]; }>>>(new Map());
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [availableTranslators, setAvailableTranslators] = useState<ITranslator[]>([]);
  
  // Refs for managing component lifecycle and caching
  const isMountedRef = useRef<boolean>(false);
  const cachedTranslationsRef = useRef<Map<string, Map<number, { paragraphs: string[]; footnotes: string[]; }>>>(new Map());

  // Load available translators once
  useEffect(() => {
    isMountedRef.current = true;
    setAvailableTranslators([]);
    setError(null);

    // Önce cache'den kontrol et
    const cachedTranslators = getFromCache<ITranslator[]>(TRANSLATORS_CACHE_KEY);
    if (cachedTranslators && cachedTranslators.length > 0) {
      setAvailableTranslators(cachedTranslators);
      console.log(`[TranslationLoader] ${cachedTranslators.length} translators loaded from cache`);
      return;
    }

    fetchDataFromR2<{ [id: string]: string }>('quran/translators.json')
      .then((data) => {
        if (!isMountedRef.current) return;
        
        if (typeof data !== 'object' || data === null || Array.isArray(data)) {
          throw new Error('Translator data has invalid format');
        }
        
        const translators = Object.entries(data)
          .map(([id, name]) => ({ id, name }))
          .sort((a, b) => a.name.localeCompare(b.name, 'tr'));
        
        setAvailableTranslators(translators);
        console.log(`[TranslationLoader] ${translators.length} translators loaded`);
        
        // Cache'e kaydet
        saveToCache(TRANSLATORS_CACHE_KEY, translators);
      })
      .catch((err) => {
        console.error("[TranslationLoader] Error loading translators:", err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('Failed to load translators'));
        }
      });

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Load translations for selected translators
  useEffect(() => {
    if (!isMountedRef.current) return;
    
    // Skip if using Arabic-only mode (1) or invalid surah
    if (contentMode === '1' || !surahId || surahId < 1 || surahId > 114) {
      setLoading(false);
      return;
    }

    // Skip if no translators selected
    if (selectedTranslators.length === 0) {
      setLoading(false);
      return;
    }

    // Wait until available translators are loaded
    if (availableTranslators.length === 0) {
      setLoading(true);
      return;
    }

    setLoading(true);
    console.log(`[TranslationLoader] Loading translations for surah ${surahId}, ${selectedTranslators.length} translators selected`);

    // Create a new map for the current surah's translations
    const newTranslationsMap = new Map<number, Map<string, { paragraphs: string[]; footnotes: string[]; }>>();
    const translationsToFetch: string[] = [];

    // Check which translators need to be fetched
    selectedTranslators.forEach(translatorId => {
      // Önce RAM cache'e bak
      const ramCacheKey = `${translatorId}-${surahId}`;
      if (cachedTranslationsRef.current.has(ramCacheKey)) {
        return; // RAM cache'de varsa, fetch'e gerek yok
      }
      
      // Sonra localStorage cache'e bak
      const storageKey = getTranslationCacheKey(translatorId, surahId);
      const cachedData = getFromCache<TranslatorSurahData>(storageKey);
      
      if (cachedData) {
        // Cache'den veriyi RAM'e yükle
        const translatorVerseMap = new Map<number, { paragraphs: string[]; footnotes: string[]; }>();
        cachedData.forEach(item => {
          translatorVerseMap.set(item.verse_no, {
            paragraphs: [item.text],
            footnotes: item.footnote ? [item.footnote] : []
          });
        });
        cachedTranslationsRef.current.set(ramCacheKey, translatorVerseMap);
      } else {
        // Cache'de yoksa fetch listesine ekle
        translationsToFetch.push(translatorId);
      }
    });

    // If all translations are already cached, use the cache
    if (translationsToFetch.length === 0) {
      console.log('[TranslationLoader] Using cached translations');
      
      // Build translation map from cache for selected translators
      selectedTranslators.forEach(translatorId => {
        const cacheKey = `${translatorId}-${surahId}`;
        const translatorData = cachedTranslationsRef.current.get(cacheKey);
        
        if (translatorData) {
          translatorData.forEach((verseData, verseNo) => {
            if (!newTranslationsMap.has(verseNo)) {
              newTranslationsMap.set(verseNo, new Map());
            }
            const verseMap = newTranslationsMap.get(verseNo)!;
            verseMap.set(translatorId, verseData);
          });
        }
      });
      
      setTranslationsMap(newTranslationsMap);
      setLoading(false);
      return;
    }

    // Fetch needed translations
    const fetchPromises = translationsToFetch.map(translatorId => {
      const path = `quran/translations/${translatorId}/${surahId}.json`;
      return fetchDataFromR2<TranslatorSurahData>(path)
        .then(data => {
          if (!Array.isArray(data)) {
            console.warn(`[TranslationLoader] Invalid data format for ${path}`);
            return { translatorId, data: null };
          }
          return { translatorId, data };
        })
        .catch(err => {
          console.error(`[TranslationLoader] Error fetching ${path}:`, err);
          return { translatorId, data: null };
        });
    });

    Promise.all(fetchPromises)
      .then(results => {
        if (!isMountedRef.current) return;

        // Process results
        results.forEach(({ translatorId, data }) => {
          if (!data) return;
          
          // Save to localStorage cache
          const storageKey = getTranslationCacheKey(translatorId, surahId);
          saveToCache(storageKey, data);
          
          // Cache in RAM
          const ramCacheKey = `${translatorId}-${surahId}`;
          const translatorVerseMap = new Map<number, { paragraphs: string[]; footnotes: string[]; }>();
          
          data.forEach(item => {
            translatorVerseMap.set(item.verse_no, {
              paragraphs: [item.text],
              footnotes: item.footnote ? [item.footnote] : []
            });
          });
          
          cachedTranslationsRef.current.set(ramCacheKey, translatorVerseMap);
        });
        
        // Now build the complete translations map from all cached data
        selectedTranslators.forEach(translatorId => {
          const cacheKey = `${translatorId}-${surahId}`;
          const translatorData = cachedTranslationsRef.current.get(cacheKey);
          
          if (translatorData) {
            translatorData.forEach((verseData, verseNo) => {
              if (!newTranslationsMap.has(verseNo)) {
                newTranslationsMap.set(verseNo, new Map());
              }
              const verseMap = newTranslationsMap.get(verseNo)!;
              verseMap.set(translatorId, verseData);
            });
          }
        });
        
        setTranslationsMap(newTranslationsMap);
      })
      .catch(err => {
        console.error('[TranslationLoader] Error processing translations:', err);
        setError(err instanceof Error ? err : new Error('Error loading translations'));
      })
      .finally(() => {
        if (isMountedRef.current) {
          setLoading(false);
        }
      });

    return () => {
      // Nothing to clean up in this effect beyond isMountedRef
    };
  }, [surahId, contentMode, selectedTranslators, availableTranslators]);

  // convert the Map structure to VerseTranslationsMap expected by consumers
  const translations = useMemo(() => {
    const result: VerseTranslationsMap = {};
    
    translationsMap.forEach((translatorMap, verseNo) => {
      const verseKey = String(verseNo);
      result[verseKey] = {};
      
      translatorMap.forEach((data, translatorId) => {
        if (selectedTranslators.includes(translatorId)) {
          result[verseKey][translatorId] = {
            paragraphs: data.paragraphs,
            footnotes: data.footnotes
          };
        }
      });
    });
    
    return result;
  }, [translationsMap, selectedTranslators]);

  return {
    translations,
    loading,
    error,
    availableTranslators,
    translationsMap
  };
} 