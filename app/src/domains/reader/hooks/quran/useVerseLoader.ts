import { useState, useEffect, useMemo } from 'react';
import {
  UseVerseLoaderReturn,
  CombinedVerseData,
  VerseWord,
  VerseArabicChunk,
  VerseWordMeaningsChunk,
  VerseWordAnalysisChunk,
  // Surah // Removed unused Surah type import
} from '@reader/models/types';
import { useTranslationLoader } from './useTranslationLoader';
import { ContentMode } from './useContentMode';
import { useSurahs } from './useSurahs'; // Sure bilgisini almak için
import { fetchDataFromR2 } from '@shared/utils/r2DataFetcher';

// TypeScript için returnType özelliğini güncelle
// UseVerseLoaderReturn türünü güncelle
interface UseVerseLoaderReturnWithTranslationsLoading extends UseVerseLoaderReturn {
  translationsLoading: boolean;
}

// Cache için yardımcı fonksiyonlar
const CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 saat (milisaniye cinsinden)
const CACHE_VERSION = 'v1'; // Cache versiyonu, veri yap<PERSON><PERSON><PERSON>ş<PERSON>e bunu güncelleme

interface CacheMetadata {
  timestamp: number;
  version: string;
}

interface CachedData<T> {
  data: T;
  metadata: CacheMetadata;
}

// Cache anahtarını oluştur
function createCacheKey(surahId: number, dataType: string): string {
  return `quran_data_${CACHE_VERSION}_${dataType}_${surahId}`;
}

// Veriyi cacheden al
function getFromCache<T>(cacheKey: string): T | null {
  try {
    const cachedItem = localStorage.getItem(cacheKey);
    if (!cachedItem) return null;
    
    const parsed = JSON.parse(cachedItem) as CachedData<T>;
    
    // Cache versiyonu kontrol et
    if (parsed.metadata.version !== CACHE_VERSION) {
      console.log(`[Cache] Outdated cache version for ${cacheKey}, clearing`);
      localStorage.removeItem(cacheKey);
      return null;
    }
    
    // Son kullanma tarihini kontrol et
    const now = Date.now();
    if (now - parsed.metadata.timestamp > CACHE_EXPIRY_TIME) {
      console.log(`[Cache] Expired cache for ${cacheKey}, clearing`);
      localStorage.removeItem(cacheKey);
      return null;
    }
    
    console.log(`[Cache] Hit for ${cacheKey}`);
    return parsed.data;
  } catch (error) {
    console.error(`[Cache] Error reading from cache for ${cacheKey}:`, error);
    return null;
  }
}

// Veriyi cache'e kaydet
function saveToCache<T>(cacheKey: string, data: T): void {
  try {
    const cachedData: CachedData<T> = {
      data,
      metadata: {
        timestamp: Date.now(),
        version: CACHE_VERSION
      }
    };
    
    localStorage.setItem(cacheKey, JSON.stringify(cachedData));
    console.log(`[Cache] Saved data to ${cacheKey}`);
  } catch (error) {
    console.error(`[Cache] Error saving to cache for ${cacheKey}:`, error);
    // localStorage doluysa veya başka bir hata varsa, cache temizleme denenebilir
    try {
      localStorage.removeItem(cacheKey);
    } catch {
      // Sessizce görmezden gel
    }
  }
}

/** Fetches the complete data for a surah for a specific data type */
async function fetchSurahData<T>(surahId: number, dataType: 'verses' | 'word_turkish_meaning' | 'word_analysis'): Promise<T[] | null> {
  // Önce cache'den kontrol et
  const cacheKey = createCacheKey(surahId, dataType);
  const cachedData = getFromCache<T[]>(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  // Cache'de yoksa fetchle
  const path = `quran/${dataType}/${surahId}.json`;
  try {
    const data = await fetchDataFromR2<T[]>(path);
    if (!Array.isArray(data)) {
        console.warn(`[fetchSurahData] Invalid data format (not array) for ${path}:`, data);
        return null;
    }
    
    // Başarılı veriyi cache'e kaydet
    saveToCache(cacheKey, data);
    
    return data as T[];
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      console.warn(`[fetchSurahData] Data not found (404): ${path}`);
      return null; 
    }
    console.error(`[fetchSurahData] Network or parsing error fetching ${path}:`, error);
    return null;
  }
}

// --- The Hook --- 
export function useVerseLoader(
  surahIdParam: string | undefined,
  contentMode: ContentMode,
  selectedTranslators: string[] = [] // Added parameter for selected translators
): UseVerseLoaderReturnWithTranslationsLoading {
  const [combinedVerses, setCombinedVerses] = useState<CombinedVerseData[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const surahIdNum = surahIdParam ? parseInt(surahIdParam, 10) : null;

  // Get Surah info (needed for total verse count)
  const { data: surahs, loading: surahsLoading, error: surahsError } = useSurahs();
  const currentSurahInfo = useMemo(() => {
      if (!surahs || !surahIdNum) return null;
      return surahs.find(s => s.id === surahIdNum);
  }, [surahs, surahIdNum]);

  // Calculate total verse count for the current surah
  const currentTotalVerseCount = useMemo(() => currentSurahInfo?.verse_count || null, [currentSurahInfo]);

  // Load translations using the updated hook, passing selectedTranslators
  const {
    translations: translationsByVerse,
    loading: translationsLoading,
    error: translationsError,
    availableTranslators
  } = useTranslationLoader(surahIdNum, contentMode, selectedTranslators);

  // Effect to load and combine verse data (Simplified for single file fetch)
  useEffect(() => {
    // Reset state when dependencies change
    setCombinedVerses(null);
    setError(null);
    setLoading(true);

    // Handle invalid Surah ID
    if (!surahIdNum || isNaN(surahIdNum) || surahIdNum < 1 || surahIdNum > 114) {
      setError(new Error('Geçersiz sure numarası.'));
      setLoading(false);
      return;
    }

    // Handle error fetching surah list
     if (surahsError) {
       console.error("[VerseLoader] Error fetching surah list:", surahsError);
       setError(new Error('Sure listesi yüklenemedi.'));
       setLoading(false);
       return;
     }

    // Wait for surah info (totalVerseCount) to be available
    if (surahsLoading || !currentSurahInfo) { // Also wait for currentSurahInfo
        // Keep loading true, wait for surah data
        return;
    }
    // Now we have totalVerseCount from currentSurahInfo
    const currentTotalVerses = currentSurahInfo.verse_count;
    if (currentTotalVerses === null || currentTotalVerses <= 0) {
        console.warn(`[VerseLoader] Surah ${surahIdNum} has invalid verse count: ${currentTotalVerses}`);
        setError(new Error(`Sure ${surahIdNum} için ayet sayısı geçersiz.`));
        setLoading(false);
        return;
    }

    // Wait for translations to finish loading or erroring
    if (translationsLoading) {
      // Keep loading true, wait for translations
      return;
    }

    // Handle translation loading errors
    if (translationsError) {
      console.error(`[VerseLoader] Translation loading error:`, translationsError);
      // Decide if translation error is fatal or just partial data
      setError(translationsError); // Report the error
      // setLoading(false); // Optionally stop loading here, or continue to load other verse data
    }

    let isMounted = true;

    const loadAndCombineData = async () => {
      try {
        // Determine needed data types based on contentMode
        const needsArabic = contentMode === '1' || contentMode === '2';
        const needsMeaning = contentMode === '3';
        const needsAnalysis = contentMode === '3';

        // Fetch complete surah data instead of chunks
        const arabicPromise = needsArabic ? fetchSurahData<VerseArabicChunk>(surahIdNum, 'verses') : Promise.resolve(null);
        const meaningPromise = needsMeaning ? fetchSurahData<VerseWordMeaningsChunk>(surahIdNum, 'word_turkish_meaning') : Promise.resolve(null);
        const analysisPromise = needsAnalysis ? fetchSurahData<VerseWordAnalysisChunk>(surahIdNum, 'word_analysis') : Promise.resolve(null);

        console.log(`[VerseLoader] Fetching complete verse data for Surah ${surahIdNum}...`);

        // Fetch all data concurrently
        const results = await Promise.allSettled([
            arabicPromise,
            meaningPromise,
            analysisPromise
        ]);

        if (!isMounted) return;

        // Helper to process settled results
        const processResult = <T>(result: PromiseSettledResult<T[] | null>): T[] | null => {
           if (result.status === 'fulfilled') {
             return result.value; // Value is T[] | null
           } else {
             console.error("[VerseLoader] Data fetch promise rejected:", result.reason);
             // Depending on requirements, you might want to throw an error here
             return null; // Or treat rejection as missing data
           }
        };

        // Process the results
        const allArabicData = processResult<VerseArabicChunk>(results[0]);
        const allMeaningData = processResult<VerseWordMeaningsChunk>(results[1]);
        const allAnalysisData = processResult<VerseWordAnalysisChunk>(results[2]);

        // --- Combine Data --- 
        const combined: CombinedVerseData[] = [];
        const arabicTextMap = new Map((allArabicData || []).map(item => [item.verse_no, item.arabic_text]));
        const meaningWordsMap = new Map((allMeaningData || []).map(item => [item.verse_no, item.words]));
        const analysisWordsMap = new Map((allAnalysisData || []).map(item => [item.verse_no, item.words]));

        for (let verseNo = 1; verseNo <= currentTotalVerses; verseNo++) {
            const arabicText = arabicTextMap.get(verseNo) || null;
            const meaningWordsArray = meaningWordsMap.get(verseNo); 
            const analysisWordsArray = analysisWordsMap.get(verseNo); 
            const verseTranslations = translationsByVerse && translationsByVerse[String(verseNo)] ? translationsByVerse[String(verseNo)] : null;

            let combinedVerseWords: VerseWord[] | undefined = undefined;

            if (contentMode === '3' && meaningWordsArray) {
                combinedVerseWords = meaningWordsArray.map((wordMeaning, index) => {
                    const analysis = analysisWordsArray?.[index];
                    return {
                        arabic: wordMeaning.arabic_word,
                        meaning: wordMeaning.turkish_meaning,
                        analysis: analysis 
                    };
                });
            }

            combined.push({
                verse_no: verseNo,
                arabic_text: arabicText,
                words: combinedVerseWords,
                translations: verseTranslations
            });
        }

        if (isMounted) {
          setCombinedVerses(combined);
          console.log(`[VerseLoader] Successfully combined data for ${combined.length} verses in Surah ${surahIdNum}.`);
        }

      } catch (err) {
        console.error(`[VerseLoader] Error loading or combining verse data for Surah ${surahIdNum}:`, err);
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Sure verileri yüklenirken bilinmeyen bir hata oluştu.'));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadAndCombineData(); // Execute data loading

    return () => {
      isMounted = false; // Prevent state updates if component unmounts during async operations
    };
  }, [surahIdNum, contentMode, translationsByVerse, translationsLoading, translationsError, surahsLoading, surahsError, currentSurahInfo]);

  // Return hook data
  return {
    verses: combinedVerses,
    loading,
    error,
    availableTranslators: availableTranslators || [],
    totalVerseCount: currentTotalVerseCount,
    surahInfo: currentSurahInfo || null,
    translationsLoading
  };
} 