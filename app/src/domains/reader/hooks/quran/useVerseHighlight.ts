import { useEffect, useRef } from 'react';

interface UseVerseHighlightProps {
  verseId: string | null;
  highlightColor: string;
  onHighlightComplete: () => void;
}

/**
 * Hook to apply a temporary background highlight effect to a specific verse element.
 * The effect sequence is: 1s highlight -> 0.3s pause -> 0.6s highlight -> off.
 */
export const useVerseHighlight = ({
  verseId,
  highlightColor,
  onHighlightComplete,
}: UseVerseHighlightProps) => {
  const timeoutIds = useRef<NodeJS.Timeout[]>([]);

  useEffect(() => {
    // Cleanup function defined first to be accessible in the effect logic
    const cleanup = () => {
      timeoutIds.current.forEach(clearTimeout); // Clear all scheduled timeouts
      timeoutIds.current = [];
      // Attempt to reset style on cleanup if element still exists
      if (verseId) { // Check if verseId was set before trying to find element
        try {
          const el = document.getElementById(verseId);
          if (el) {
            el.style.backgroundColor = '';
            el.style.transition = '';
            el.style.padding = '';
            el.style.borderRadius = '';
            el.style.margin = '';
          }
        } catch {
          // Ignore errors if element is gone during cleanup
        }
      }
    };

    // Clear previous timeouts before starting new sequence
    cleanup();

    if (!verseId || !highlightColor) {
      return; // No verse ID or color, do nothing (cleanup already handled)
    }

    const element = document.getElementById(verseId);
    if (!element) {
      console.warn(`[useVerseHighlight] Element with ID "${verseId}" not found.`);
      // We don't call onHighlightComplete here anymore, 
      // it should only be called when the animation finishes successfully.
      return; // Element not found, do nothing further
    }

    // --- Apply Animation Sequence ---
    let isMounted = true; // Flag to prevent style updates if component unmounts

    // Store original padding if any
    const originalPadding = window.getComputedStyle(element).padding;
    const originalMargin = window.getComputedStyle(element).margin;
    
    // 1. Apply initial highlight (duration: 1s)
    element.style.backgroundColor = highlightColor;
    element.style.transition = 'background-color 0.2s ease-out'; // Smooth transition
    element.style.padding = '1rem 0rem'; // Sadece dikey padding bıraktım, yatay padding 0
    element.style.borderRadius = '12px'; // Rounded corners
    element.style.margin = '-0.5rem 0'; // Yatay margin'i de 0 yaptım

    const t1 = setTimeout(() => {
      if (!isMounted || !element) return;
      element.style.backgroundColor = ''; // Remove highlight

      // 2. Pause (duration: 0.3s)
      const t2 = setTimeout(() => {
        if (!isMounted || !element) return;
        
        // 3. Apply second highlight (duration: 0.6s)
        element.style.backgroundColor = highlightColor;

        const t3 = setTimeout(() => {
          if (!isMounted || !element) return;
          element.style.backgroundColor = ''; // Remove final highlight
          element.style.transition = ''; // Reset transition
          element.style.padding = originalPadding; // Restore original padding
          element.style.borderRadius = ''; // Reset border radius
          element.style.margin = originalMargin; // Restore original margin
          if (isMounted) {
            onHighlightComplete(); // Signal completion ONLY when animation finishes
          }
        }, 600); // 0.6s second highlight duration
        timeoutIds.current.push(t3);

      }, 300); // 0.3s pause duration
      timeoutIds.current.push(t2);

    }, 1000); // 1s first highlight duration
    timeoutIds.current.push(t1);
    
    // Return the cleanup function
    return () => {
        isMounted = false;
        cleanup(); // Use the defined cleanup function
    };
  }, [verseId, highlightColor, onHighlightComplete]); // Rerun effect if these change
}; 