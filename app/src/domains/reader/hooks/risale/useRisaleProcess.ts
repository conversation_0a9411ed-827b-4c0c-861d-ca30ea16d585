import { useMemo, useCallback } from 'react';
import { SectionStateData, RisaleArabicPhrase, RisaleDictionaryItem, RisaleSentence } from '@reader/models/types';

// Helper function to escape regex special characters
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export function useRisaleProcess(sectionData: SectionStateData | null) {
  const contentData = sectionData?.content;

  const dictionary = useMemo(() => contentData?.dictionary || [], [contentData]);
  const footnotes = useMemo(() => contentData?.footnotes || [], [contentData]);

  const sortedDictionary = useMemo(() => {
    if (!dictionary.length) return [];
    return [...dictionary].sort((a, b) => b.word.length - a.word.length);
  }, [dictionary]);

  const sortedArabicPhrases = useMemo(() => {
    if (!contentData?.arabic_phrases?.length) return [];
    return [...contentData.arabic_phrases].sort((a, b) => b.arabic.length - a.arabic.length);
  }, [contentData?.arabic_phrases]);

  const processText = useCallback((text: string): string => {
    if (!text?.trim()) return text;

    let normalizedParagraph = text.normalize('NFC');
    const arabicPhrasePlaceholders: Map<string, string> = new Map();
    let placeholderIndex = 0;

    if (sortedArabicPhrases.length > 0) {
      sortedArabicPhrases.forEach((phrase: RisaleArabicPhrase) => {
        const normalizedPhraseArabic = phrase.arabic.normalize('NFC');
        try {
          const phraseRegex = new RegExp(escapeRegExp(normalizedPhraseArabic), 'g');
          normalizedParagraph = normalizedParagraph.replace(phraseRegex, (match) => {
            const placeholder = `%%ARABIC_PHRASE_${placeholderIndex}%%`;
            const interactivePhrase = `<span class="interactive-arabic-phrase" data-arabic-text="${encodeURIComponent(phrase.arabic)}" data-arabic-translation="${encodeURIComponent(phrase.translation)}">${match}</span>`;
            arabicPhrasePlaceholders.set(placeholder, interactivePhrase);
            placeholderIndex++;
            return placeholder;
          });
        } catch (e) {
          console.error("Error creating regex for arabic phrase:", phrase.arabic, e);
        }
      });
    }

    let processedParagraph = normalizedParagraph;

    if (sortedDictionary.length > 0) {
      sortedDictionary.forEach((item: RisaleDictionaryItem) => {
        const normalizedItemWord = item.word.normalize('NFC');
        const regex = new RegExp(`(?<![\\w>])\\b(${escapeRegExp(normalizedItemWord)})\\b(?![\\w<])`, 'gi');
        
        let tempParagraph = "";
        const parts = processedParagraph.split(/(%%ARABIC_PHRASE_\d+%%)/g);

        parts.forEach(part => {
          if (arabicPhrasePlaceholders.has(part)) {
            tempParagraph += part;
          } else {
            tempParagraph += part.replace(regex, (match) => {
                return `<span class="interactive-word" data-dict-word="${item.word}">${match}</span>`;
            });
          }
        });
        processedParagraph = tempParagraph;
      });
    }
    
    if (footnotes && footnotes.length > 0) { 
      processedParagraph = processedParagraph.replace(
        /<sup><b>(\d+)<\/b><\/sup>/g, 
        (_, p1) => `<sup class="interactive-footnote" data-footnote-num="${p1}"><b>${p1}</b></sup>`
      );
    }

    arabicPhrasePlaceholders.forEach((interactivePhrase, placeholder) => {
      processedParagraph = processedParagraph.replace(placeholder, interactivePhrase);
    });
    
    return processedParagraph;
  }, [sortedDictionary, sortedArabicPhrases, footnotes]);

  const processedSentences: RisaleSentence[] = useMemo(() => {
    if (!contentData?.sentences) return [];
    
    console.log('[DEBUG] useRisaleProcess: Sentences received from parent:', JSON.parse(JSON.stringify(contentData.sentences)));

    const result = contentData.sentences.map(sentence => ({
      ...sentence,
      text: processText(sentence.text || ''),
    }));

    console.log('[DEBUG] useRisaleProcess: Sentences after processing:', JSON.parse(JSON.stringify(result)));

    return result;
  }, [contentData?.sentences, processText]);

  return {
    processedSentences,
    dictionary,
    footnotes,
  };
} 