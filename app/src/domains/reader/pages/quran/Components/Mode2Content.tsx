import React from 'react';
import { CombinedVerseData, ITranslator } from '@reader/models/types';
import { TranslationDisplay } from './TranslationDisplay';
import { MoreVertical } from 'lucide-react';

interface Mode2ContentProps {
  combinedVerse: CombinedVerseData;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  sectionLeftBorderColor: string;
  translatorTitleColor: string;
  footnoteColor: string;
  verseNumberBg: string;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
  surahName?: string;
}

export const Mode2Content: React.FC<Mode2ContentProps> = ({
  combinedVerse,
  selectedTranslators,
  availableTranslators,
  sectionLeftBorderColor,
  translatorTitleColor,
  footnoteColor,
  verseNumberBg,
  onVerseMenuClick,
  openMenuVerseKey: _openMenuVerseKey,
  onCloseVerseMenu: _onCloseVerseMenu,
  surahName: _surahName,
}) => {
  if (!combinedVerse) {
    return null;
  }

  const verseKey = `${combinedVerse.verse_no}`;

  return (
    <div className="py-4 border-b border-border-color relative">
      <div
        className="text-right font-arabic text-2xl leading-loose mb-4"
        style={{ direction: 'rtl', unicodeBidi: 'embed' }}
      >
        {combinedVerse.arabic_text}

        {combinedVerse.sajdah && (
          <span className="text-amber-500 mx-2" title="Secde Ayeti">۩</span>
        )}

        <div className="inline-flex items-center" style={{ marginLeft: '3rem' }}>
          <span
            className="inline-flex items-center justify-center relative px-2 pt-[0.35rem] pb-[0.15rem] rounded-lg text-sm mr-1"
            style={{
              color: 'var(--text-color)',
              backgroundColor: verseNumberBg
            }}
          >
            <span>{combinedVerse.verse_no}</span>
          </span>

          {onVerseMenuClick && (
            <button
              data-verse-menu-button={verseKey}
              onClick={(e) => {
                e.stopPropagation();
                onVerseMenuClick(verseKey, e);
              }}
              className="p-0.5 rounded-full hover:bg-[var(--text-color)]/10 transition-colors duration-150"
              style={{ color: 'var(--text-color)' }}
            >
              <MoreVertical size={14} />
            </button>
          )}
        </div>
      </div>

      <TranslationDisplay
        verse={combinedVerse}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        sectionLeftBorderColor={sectionLeftBorderColor}
        translatorTitleColor={translatorTitleColor}
        footnoteColor={footnoteColor}
        displayMode="2"
      />
    </div>
  );
}; 