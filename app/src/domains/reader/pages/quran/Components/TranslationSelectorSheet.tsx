import React, { useRef, useEffect, useMemo, useState, useLayoutEffect } from 'react';
import { Languages, ChevronDown, Search, X } from 'lucide-react'; // Added X for close button
import { FixedSizeList as List } from 'react-window';
import { useAutoOverlay } from '@shared/hooks/autooverlay'; // IMPORT autoOverlay

// Generic interface for selectable items
interface ISelectableItem {
    id: string;
    name: string;
}

interface TranslationSelectorSheetProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  availableItems: ISelectableItem[] | undefined;
  selectedItems: string[]; // Array of selected item IDs
  onSelectionChange: (newSelection: string[]) => void;
  triggerButtonLabel?: string; // Optional label for the trigger button
  sheetTitle?: string; // Optional title for the sheet
}

// Debounce delay in milliseconds
const DEBOUNCE_DELAY = 300;

// Row height for virtualized list
const ROW_HEIGHT = 40;

export const TranslationSelectorSheet: React.FC<TranslationSelectorSheetProps> = ({
  isOpen,
  setIsOpen,
  searchQuery,
  setSearchQuery,
  availableItems,
  selectedItems,
  onSelectionChange,
  triggerButtonLabel = "Select Items", // Default trigger label
  sheetTitle = "Select Translations" // Default sheet title
}) => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const listContainerRef = useRef<HTMLDivElement>(null);

  // State for list height
  const [listHeight, setListHeight] = useState(0);

  // State for debounced search query
  const [debouncedSearch, setDebouncedSearch] = useState(searchQuery);

  // Calculate colors using autoOverlay like in NavigationSheet
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  // Apply debounce to the search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, DEBOUNCE_DELAY);

    return () => {
      clearTimeout(timer);
    };
  }, [searchQuery]);

  // Calculate list height dynamically
  useLayoutEffect(() => {
    if (isOpen && listContainerRef.current) {
      const height = listContainerRef.current.clientHeight;
      setListHeight(height);
    }
  }, [isOpen]);

  // Filtered items - useMemo for optimization
  const filteredItems = useMemo(() => {
    return availableItems?.filter(item => {
      const nameLower = item.name.toLowerCase();
      const idLower = item.id.toLowerCase();
      const searchLower = debouncedSearch.toLowerCase();
      // Search in both name and ID
      return nameLower.includes(searchLower) || idLower.includes(searchLower);
    }) || [];
  }, [availableItems, debouncedSearch]);

  // Close sheet on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click is outside the sheetRef element
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
         // Check if the click is on the trigger button itself (we don't want outside click handler to close it then)
         const triggerButton = (event.target as Element)?.closest('[data-trigger-button="translation-selector-sheet"]');
         if (!triggerButton) {
            setIsOpen(false);
         }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
    return undefined;
  }, [isOpen, setIsOpen]);

  // Render a row in the virtualized list
  const ItemRow = ({ index, style }: { index: number, style: React.CSSProperties }) => {
    const item = filteredItems[index];
    const displayName = item.name;
    const isSelected = selectedItems.includes(item.id);

    return (
      <div
        style={style}
        className={`flex items-center px-4 py-2.5 text-sm cursor-pointer relative hover:bg-[var(--text-color)]/5 border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)] last:border-b-0 ${
          isSelected ? 'bg-[var(--text-color)]/[0.08]' : '' // Active background
        }`}
        onClick={() => {
          let newSelection;
          if (isSelected) {
            newSelection = selectedItems.filter(id => id !== item.id);
          } else {
            newSelection = [...selectedItems, item.id];
          }
          onSelectionChange(newSelection);
        }}
      >
        <div className="flex-1" style={{ color: 'var(--text-color)' }}>{displayName}</div>
        <div
          className={`w-5 h-5 rounded border flex items-center justify-center ${
            isSelected
              ? 'bg-[var(--text-color)] border-[var(--text-color)]'
              : 'border-[var(--text-color)]'
          }`}
        >
          {isSelected && (
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" style={{ color: 'var(--bg-color)' }}>
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          )}
        </div>
      </div>
    );
  };

  // Trigger button
  const renderTriggerButton = () => (
    <button
      onClick={() => setIsOpen(prev => !prev)}
      className="px-2 py-1.5 rounded-lg flex items-center gap-1"
      style={{ color: 'var(--text-color)' }}
      aria-label={sheetTitle}
      // Disable button if no items are available
      disabled={!availableItems || availableItems.length === 0}
      data-trigger-button="translation-selector-sheet" // Add data attribute for outside click logic
    >
      <Languages size={18} /> {/* Keep icon or change as needed */}
      {triggerButtonLabel && <span>{triggerButtonLabel}</span>}
      {triggerButtonLabel && <ChevronDown size={14} className="ml-0.5 transition-transform" />}
    </button>
  );

  // The selection sheet/modal
  const renderSheet = () => {
    if (!isOpen || !availableItems) return null;

    return (
      <>
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-40 transition-opacity"
          style={{ opacity: isOpen ? 1 : 0 }}
          onClick={() => setIsOpen(false)} // Use onClose passed from parent or setIsOpen(false)
        />

        {/* Sheet Container - Apply NavigationSheet styles */}
        <div
          ref={sheetRef}
          className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 overflow-hidden flex flex-col" // Added overflow-hidden and flex flex-col
          style={{
            backgroundColor: navBgColor,
            borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
          }}
        >
          {/* Pull indicator for mobile */}
          <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />

          {/* Header - Apply NavigationSheet styles */}
          <div className="flex items-center justify-between p-3 border-b flex-shrink-0" style={{ borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}>
            <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>{sheetTitle}</h3>
            <button onClick={() => setIsOpen(false)} className="p-1 rounded-full hover:bg-[var(--text-color)]/10" aria-label="Close">
                <X size={18} style={{ color: 'var(--text-color)' }}/>
            </button>
          </div>

          {/* Content Area - Apply NavigationSheet styles */}
          <div className="px-4 pt-3 pb-4 overflow-y-auto flex-grow flex flex-col"> {/* Adjusted padding and flex-grow */}
            {/* Search box - Apply NavigationSheet styles */}
            <div className="relative mb-2.5 flex-shrink-0"> {/* Added margin bottom */}
              <input
                type="text"
                placeholder="Meal ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full px-4 py-2.5 pl-9 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70`}
                style={{
                  backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                }}
              />
              <Search 
                size={16} 
                className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
              />
              {searchQuery !== debouncedSearch && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs opacity-70 animate-pulse">...</div>
              )}
            </div>

            {/* List Area - Apply NavigationSheet styles */}
            <div
              ref={listContainerRef}
              className="rounded-xl overflow-hidden flex-grow"
              style={{ backgroundColor: listAreaBgColor }}
            >
              {filteredItems.length > 0 ? (
                <List
                  height={listHeight}
                  itemCount={filteredItems.length}
                  itemSize={ROW_HEIGHT} // Ensure ROW_HEIGHT matches ItemRow styling
                  width="100%"
                  className="scrollbar-thin scrollbar-thumb-[var(--text-color)]/20 scrollbar-track-transparent"
                  style={{ 
                    overflowX: 'hidden', 
                    overscrollBehavior: 'contain' 
                  }}
                >
                  {ItemRow}
                </List>
              ) : (
                <div className="h-[100px] flex items-center justify-center text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
                  No results found
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="relative"> {/* Keep relative positioning if the trigger is inline */}
      {renderTriggerButton()}
      {renderSheet()}
    </div>
  );
};

export default TranslationSelectorSheet; 