import React, { useState, useEffect } from 'react';
import {
  Copy,
  Bookmark,
  FileText,
  ScanText,
  Check,
} from 'lucide-react';
import { CombinedVerseData, ITranslator } from '@reader/models/types';

import { AnnotationToast } from '@domains/reader-interactions/shared/components/AnnotationToast';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useReaderStore } from '@domains/reader/store/readerstore';

// Yeni oluşturduğumuz bileşenleri ve hook'u import ediyoruz
import { useBreakpoint } from '@shared/hooks/useBreakpoint';
import { MobileActionSheet } from './MobileActionSheet';
import { DesktopActionPopover, ActionMenuItem } from './DesktopActionPopover'; // ActionMenuItem tipini de import et

// Props arayüzünü güncelliyoruz
interface UnifiedActionMenuProps {
  isOpen?: boolean;
  onClose?: () => void;
  verseKey?: string;
  verseData?: CombinedVerseData;
  availableTranslators?: ITranslator[];
  selectedTranslators?: string[];
  surahName?: string;
  selectionKey?: string; // Kopyalama durumunu sıfırlamak için
  // Masaüstü popover'ının konumlanacağı elementi belirtmek için
  anchorEl: HTMLElement | null;
  onOpenNoteSheet: (verseKey: string, verseData: CombinedVerseData, surahName?: string) => void;
  onOpenBookmarkSheet: (verseKey: string, verseData: CombinedVerseData, surahName?: string) => void;
}

export const UnifiedActionMenu: React.FC<UnifiedActionMenuProps> = ({
  isOpen,
  onClose,
  verseKey,
  verseData,
  availableTranslators,
  selectedTranslators,
  surahName,
  selectionKey,
  anchorEl,
  onOpenNoteSheet,
  onOpenBookmarkSheet,
}) => {
  // --- STATE VE HOOKS --- //
  const [isCopied, setIsCopied] = useState(false);
  
  
  const [toastState, setToastState] = useState({
    isVisible: false,
    message: '',
    type: 'success' as 'success' | 'error'
  });

  const { user } = useAuthStore();
  const { openWordDetailSheet } = useReaderStore();
  const breakpoint = useBreakpoint(); // Ekran boyutunu kontrol eden hook

  // --- YARDIMCI FONKSİYONLAR --- //
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  };

  const hideToast = () => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  };

  useEffect(() => {
    setIsCopied(false);
  }, [selectionKey, isOpen]);

  // --- EYLEM HANDLER'LARI --- //
  const handleCopy = () => {
    if (!verseData) return;
    const showCopiedMessage = () => {
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
        onClose?.();
      }, 1500);
    };

    let textToCopy = `${verseData.arabic_text || ''}\n\n`;
    if (verseData.translations && availableTranslators && selectedTranslators) {
      const selectedTranslationsText = selectedTranslators.map(translatorId => {
        const translator = availableTranslators.find(t => String(t.id) === translatorId);
        const translationData = verseData.translations?.[translatorId];
        if (translator && translationData?.paragraphs) {
          return `[${translator.name}]\n${translationData.paragraphs.join('\n')}`;
        }
        return '';
      }).filter(Boolean).join('\n\n');
      if (selectedTranslationsText) textToCopy += `${selectedTranslationsText}\n\n`;
    }
    textToCopy += `(${surahName || 'Sure'}, ${verseData.verse_no}. Ayet)`;
    navigator.clipboard.writeText(textToCopy).then(showCopiedMessage).catch(err => console.error("Failed to copy:", err));
  };

  const handleNoteClick = () => {
    if (!user) return showToast('Not eklemek için giriş yapmanız gerekiyor.', 'error');
    if (!verseData || !verseKey) return showToast('Ayet bilgileri eksik.', 'error');
    
    // 🎯 Önce action'ı çağır, sonra popover'ı kapat
    onOpenNoteSheet(verseKey, verseData, surahName);
    onClose?.();
  };

  const handleBookmarkClick = () => {
    if (!user) return showToast('Yer imi eklemek için giriş yapmanız gerekiyor.', 'error');
    if (!verseData || !verseKey) return showToast('Ayet bilgileri eksik.', 'error');
    onOpenBookmarkSheet(verseKey, verseData, surahName);
    onClose?.();
  };
  
  const handleWordDetailClick = () => {
    if (!verseData || !verseKey) return showToast('Ayet bilgileri eksik.', 'error');
    openWordDetailSheet(verseKey);
    onClose?.();
  };

  

  const executeAction = (e: React.MouseEvent, action: ((e: React.MouseEvent) => void) | undefined) => {
    e.stopPropagation();
    action?.(e);
  };

  // --- RENDER LOGIC --- //
  if (!isOpen) return null;

  const menuItems: ActionMenuItem[] = [
    { icon: FileText, text: 'Notlar', action: 'open_notes', handler: handleNoteClick },
    { icon: Bookmark, text: 'Kaydet', action: 'add_bookmark', handler: handleBookmarkClick },
    isCopied
      ? { icon: Check, text: 'Kopyalandı!', action: 'copy', handler: undefined, isGreen: true }
      : { icon: Copy, text: 'Kopyala', action: 'copy', handler: handleCopy },
    { icon: ScanText, text: 'Kelime Detayı', action: 'word_details', handler: handleWordDetailClick } // Güncelledik
  ];

  const [surahNum, verseNum] = verseKey?.split('-') || ['', ''];
  const sheetTitle = `${surahName || `Sure ${surahNum}`}, ${verseNum}. Ayet (${surahNum}:${verseNum})`;

  // Görünüm bileşenlerine geçirilecek ortak proplar
  const commonProps = {
    isOpen,
    onClose,
    sheetTitle,
    menuItems,
    executeAction,
  };

  return (
    <>
      {/* Ekran boyutuna göre doğru bileşeni render et */}
      {breakpoint === 'mobile' ? (
        <MobileActionSheet {...commonProps} />
      ) : (
        <DesktopActionPopover {...commonProps} anchorEl={anchorEl} />
      )}

      {/* Bu BottomSheet'ler hem mobil hem de masaüstünde ortak kullanılacak */}
      

      

      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />
    </>
  );
};