import React from 'react';
import { X, ChevronRight } from 'lucide-react';
import { Surah } from '@reader/models/types';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { autoHue } from '@shared/hooks/autohue';

// Helper function to capitalize the first letter
const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

interface NavigationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  navigationType: 'surah' | 'verse';
  onChangeNavigationType: (type: 'surah' | 'verse') => void;
  surahSearch: string;
  onChangeSurahSearch: (value: string) => void;
  verseSearch: string;
  onChangeVerseSearch: (value: string) => void;
  currentSurahId: number;
  currentVerse: number;
  filteredSurahs: Surah[];
  filteredVerses: number[];
  onSelectSurah: (surahId: number, navigateToPage?: boolean) => void;
  onSelectVerse: (verseNo: number) => void;
}

export const NavigationSheet: React.FC<NavigationSheetProps> = ({
  isOpen,
  onClose,
  navigationType,
  onChangeNavigationType,
  surahSearch,
  onChangeSurahSearch,
  verseSearch,
  onChangeVerseSearch,
  currentSurahId,
  currentVerse,
  filteredSurahs,
  filteredVerses,
  onSelectSurah,
  onSelectVerse
}) => {
  const navBgColor = autoOverlay ? useAutoOverlay(8, 'var(--bg-color)') : '#1e1e1e';
  const toolbarBgColor = autoOverlay ? useAutoOverlay(4, 'var(--bg-color)') : '#1a1a1a';
  const listAreaBgColor = autoOverlay ? useAutoOverlay(4, 'var(--bg-color)') : '#1c1c1c';
  const verseSquareBgColor = autoOverlay ? useAutoOverlay(4, 'var(--bg-color)') : '#2d2d2d';

  if (!isOpen) return null;
  
  const activeStyle = autoHue('var(--text-color)');
  const defaultStyle = { color: 'var(--text-color)' };
  
  // Sure seçip ayet görünümüne geçiş yapan ortak fonksiyon
  const selectSurahAndShowVerses = (surahId: number) => {
    // Önce seçilen sureyi güncelle - navigateToPage=false ile sayfa navigasyonunu devre dışı bırak
    onSelectSurah(surahId, false);
    
    // Sonra ayet görünümüne geç
    onChangeNavigationType('verse');
  };
  
  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />
      
      {/* Sheet - Increased rounding */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Segment Control with Close Button - Increased rounding */}
        <div className="mx-4 mt-4 flex items-center justify-between">
          <div className="flex-1 flex items-center p-1 rounded-xl overflow-hidden" style={{
            backgroundColor: toolbarBgColor
          }}>
            <button
              onClick={() => onChangeNavigationType('surah')}
              className={`flex-1 px-3 py-2 rounded-xl text-sm font-medium transition-colors ${
                navigationType === 'surah' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={navigationType === 'surah' ? activeStyle : defaultStyle}
            >
              Sure
            </button>
            <span className="text-[var(--text-color)] opacity-40 mx-1">|</span>
            <button
              onClick={() => onChangeNavigationType('verse')}
              className={`flex-1 px-3 py-2 rounded-xl text-sm font-medium transition-colors ${
                navigationType === 'verse' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={navigationType === 'verse' ? activeStyle : defaultStyle}
            >
              Ayet
            </button>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center ml-2 rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-4 pt-3 pb-1 overflow-y-auto max-h-[calc(80vh-4rem)] md:max-h-[calc(70vh-6rem)] mt-2">
          {navigationType === 'surah' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={surahSearch}
                  onChange={(e) => onChangeSurahSearch(e.target.value)}
                  placeholder="Sure adı veya numarası..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70`}
                  style={{ 
                    backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                  }}
                />
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </div>
              <div 
                className="space-y-0 p-3 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {filteredSurahs.map((s) => {
                  const surahNameStyle = s.id === currentSurahId 
                    ? activeStyle
                    : defaultStyle;
                    
                  return (
                    <button
                      key={s.id}
                      onClick={() => {
                        // Sureye tıklandığında sayfa navigasyonu yapmak istiyoruz
                        onSelectSurah(s.id, true);
                      }}
                      className={`w-full px-4 py-2.5 text-left rounded-none text-sm flex items-center justify-between relative hover:bg-[var(--text-color)]/5 text-[var(--text-color)] border-b border-[color-mix(in_srgb,var(--text-color)_15%,transparent)] last:border-b-0 ${
                        s.id === currentSurahId ? 'bg-[var(--text-color)]/[0.08]' : ''
                      }`}
                      style={defaultStyle}
                    >
                      <div className="flex items-center gap-3">
                        <span className="w-6 text-right opacity-80">{s.id}.</span>
                        <span 
                          className="truncate font-medium" 
                          style={surahNameStyle}
                        >
                          {s.name}
                        </span>
                        <span className="text-xs opacity-60">
                          {capitalizeFirstLetter(s.revelation_place)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs opacity-60">{s.verse_count} Ayet</span>
                        <div
                          className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation(); // Bu tıklamanın üst butona geçmesini engelle
                            selectSurahAndShowVerses(s.id);
                          }}
                          title={`${s.name} Suresi Ayetleri`}
                        >
                          <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {navigationType === 'verse' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={verseSearch}
                  onChange={(e) => onChangeVerseSearch(e.target.value)}
                  placeholder="Ayet numarası..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70`}
                  style={{ 
                    backgroundColor: 'color-mix(in srgb, var(--text-color) 8%, transparent)'
                  }}
                />
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </div>
              
              {/* Seçili sure başlığı ekleniyor */}
              <div className="mb-2 py-2 text-sm font-medium border-b" style={{ 
                color: 'var(--text-color)',
                borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
              }}>
                <span className="opacity-60">Sure: </span>
                <span>{filteredSurahs.find(s => s.id === currentSurahId)?.name || `Sure ${currentSurahId}`}</span>
              </div>
              
              <div className="grid grid-cols-5 gap-2 mt-3">
                {filteredVerses.map((verseNo) => {
                  // Mevcut ayet için stil
                  const isCurrentVerse = verseNo === currentVerse;
                  
                  // Arka plan her zaman verseSquareBgColor olsun, sadece yazı rengi değişsin
                  const verseStyle = { backgroundColor: verseSquareBgColor };
                  
                  // Yazı rengi stili
                  const verseTextStyle = isCurrentVerse 
                    ? autoHue('var(--text-color)') 
                    : { color: 'var(--text-color)' };

                  return (
                    <button
                      key={verseNo}
                      onClick={() => {
                        onSelectVerse(verseNo);
                        onClose();
                      }}
                      className={`aspect-square p-1 text-sm rounded-xl flex items-center justify-center font-medium transition-colors hover:opacity-80 ${
                        isCurrentVerse ? 'font-medium' : ''
                      }`}
                      style={verseStyle}
                    >
                      <span style={verseTextStyle}>{verseNo}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NavigationSheet; 