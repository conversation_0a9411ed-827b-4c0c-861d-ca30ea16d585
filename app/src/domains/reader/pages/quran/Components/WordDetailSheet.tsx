import React, { useEffect } from 'react';
import { X, ScanText } from 'lucide-react';
import { useWordAnalysis } from '@reader/hooks/useWordAnalysis';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface WordDetailSheetProps {
  isOpen: boolean;
  onClose: () => void;
  verseKey: string;
  sheetTitle: string;
}

export const WordDetailSheet: React.FC<WordDetailSheetProps> = ({ isOpen, onClose, verseKey, sheetTitle }) => {
  const { data: words, loading, error } = useWordAnalysis(verseKey);

  // Dynamic theme colors
  const sheetBgColor = autoOverlay ? useAutoOverlay(8, 'var(--bg-color)') : '#1e1e1e';
  const borderColor = autoOverlay ? useAutoOverlay(15, 'var(--bg-color)') : 'rgba(255,255,255,0.1)';

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // ESC tuşu ile kapanma
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      return () => document.removeEventListener('keydown', handleEscKey);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 mb-4" style={{ borderColor: 'var(--text-color)' }}></div>
          <div className="text-center opacity-70" style={{ color: 'var(--text-color)' }}>
            Kelime analizleri yükleniyor...
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <div className="text-center text-red-500">
            <div className="text-xl mb-2">⚠️</div>
            <div className="font-medium mb-1">Hata</div>
            <div className="text-sm opacity-80">{error.message}</div>
          </div>
        </div>
      );
    }

    if (!words || words.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <div className="text-center opacity-70" style={{ color: 'var(--text-color)' }}>
            <div className="text-xl mb-2">📚</div>
            <div>Bu ayet için kelime analizi bulunamadı.</div>
          </div>
        </div>
      );
    }

    return (
      <div className="p-4">
        <div dir="rtl" className="text-right flex flex-wrap items-start gap-x-2 gap-y-4 justify-start">
          {words.map((word) => (
            <div
              key={`${verseKey}-${word.id}`}
              className="flex flex-col items-center text-center px-1 py-1 min-w-[70px] max-w-[120px]"
            >
              {/* Kelime (kompakt) */}
              <div 
                className="text-2xl font-arabic leading-8 mb-1"
                style={{ color: 'var(--text-color)' }}
              >
                {word.arabic}
              </div>
              
              {/* Anlam (kompakt, tam uzunluk) */}
              <div 
                className="text-xs font-sans leading-tight mb-1 px-1 break-words"
                style={{ color: 'var(--text-color)' }}
              >
                {word.meaning}
              </div>

              {/* Ek bilgiler - kompakt düzen, tam uzunluk */}
              <div className="opacity-75 space-y-1 text-xs w-full">
                {/* Kök */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.kok || '-'}
                  </div>
                </div>
                {/* Türkçe Gramer */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.turkce_gramer || '-'}
                  </div>
                </div>
                {/* Arapça Gramer */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.arapca_gramer || '-'}
                  </div>
                </div>
                {/* Zaman */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.zaman || '-'}
                  </div>
                </div>
                {/* Sarf Bilgisi */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.sarf_bilgisi || '-'}
                  </div>
                </div>
                {/* Bab */}
                <div className="text-center break-words">
                  <div className="opacity-70 text-xs leading-tight" style={{ color: 'var(--text-color)' }}>
                    {word.analysis.bab || '-'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        onClick={handleBackdropClick}
        aria-hidden="true"
      />

      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[10%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[95%] md:max-w-[1200px] md:max-h-[80vh] md:rounded-2xl rounded-t-2xl shadow-2xl flex flex-col"
        style={{ 
          backgroundColor: sheetBgColor,
          borderColor: borderColor,
          borderWidth: '1px',
          borderStyle: 'solid'
        }}
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="word-detail-title"
      >
        {/* Pull indicator for mobile */}
        <div 
          className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full opacity-30"
          style={{ backgroundColor: 'var(--text-color)' }}
        />

        {/* Header */}
        <div className="mx-4 mt-4 mb-2 flex items-center justify-between flex-shrink-0">
          <div className="flex items-center gap-3">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
            >
              <ScanText size={18} style={{ color: 'var(--text-color)' }} />
            </div>
            <h3 id="word-detail-title" className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              {sheetTitle}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/10 transition-colors"
            style={{ color: 'var(--text-color)' }}
            aria-label="Kelime detay sayfasını kapat"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-grow overflow-y-auto py-2">
          {renderContent()}
        </div>
      </div>
    </>
  );
};
