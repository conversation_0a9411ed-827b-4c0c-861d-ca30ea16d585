import React, { useRef, useEffect } from 'react';
import { ChevronDown, LayoutGrid, X } from 'lucide-react';
import { ContentMode } from '@reader/hooks/quran/useContentMode';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ContentModeSelectorProps {
  contentMode: ContentMode;
  onChange: (value: ContentMode) => void;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  showText?: boolean; // Metin gösterme özelliği
}

const modes = [
  { 
    id: '1' as ContentMode, 
    title: "Kur'an", 
    description: "Sad<PERSON><PERSON> Ku<PERSON>'an-ı Kerim'in Arapça orijinal metnini gösterir." 
  },
  { 
    id: '2' as ContentMode, 
    title: "Kur'an ve Meali", 
    description: "Arap<PERSON> metnin altında, seçtiğiniz mealleri de gösterir." 
  },
  { 
    id: '3' as ContentMode, 
    title: "<PERSON><PERSON><PERSON> Meali", 
    description: "Her bir Ara<PERSON><PERSON> kelimenin üzerine tıklanabilir Türkçe anlamını gösterir." 
  },
  { 
    id: '4' as ContentMode, 
    title: "Sadece Meal", 
    description: "Sadece seçtiğiniz Türkçe mealleri gösterir, Arapça metin yer almaz." 
  },
];


export const ContentModeSelector: React.FC<ContentModeSelectorProps> = ({
  contentMode,
  onChange,
  isOpen,
  setIsOpen,
  showText = true // Varsayılan olarak metin göster
}) => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  // Dışarı tıklama için useEffect
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
        // Tetikleyici butona tıklanıp tıklanmadığını kontrol et
        const triggerButton = (event.target as Element)?.closest('[data-trigger-button="content-mode-selector"]');
        if (!triggerButton) {
          setIsOpen(false);
        }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
    return undefined;
  }, [isOpen, setIsOpen]);

  const currentMode = modes.find(m => m.id === contentMode) || modes[0];

  // İçerik modu butonunu render et
  const renderContentModeButton = () => (
    <button
      onClick={(e) => {
        e.stopPropagation();
        setIsOpen(!isOpen);
      }}
      data-trigger-button="content-mode-selector"
      className="px-2 py-1.5 rounded-lg flex items-center gap-1"
      style={{ color: 'var(--text-color)' }}
      aria-label="Görünüm Seçenekleri"
    >
      <LayoutGrid size={16} />
      {showText && <span className="text-[15px]">{currentMode.title}</span>}
      {showText && <ChevronDown size={14} className="ml-0.5 transition-transform" />}
    </button>
  );

  // İçerik modu sheet'ini render et
  const renderContentModeSheet = () => {
    if (!isOpen) return null;

    return (
      <>
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-40 transition-opacity"
          style={{ opacity: isOpen ? 1 : 0 }}
          onClick={() => setIsOpen(false)}
        />
        
        {/* Sheet */}
        <div
          ref={sheetRef}
          className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 flex flex-col"
          style={{ 
            backgroundColor: navBgColor,
            borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
          }}
        >
          {/* Pull indicator for mobile */}
          <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />

          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b flex-shrink-0" style={{ borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}>
            <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>Görünüm Seçenekleri</h3>
            <button onClick={() => setIsOpen(false)} className="p-1 rounded-full hover:bg-[var(--text-color)]/10" aria-label="Kapat">
                <X size={18} style={{ color: 'var(--text-color)' }}/>
            </button>
          </div>

          {/* Content Area */}
          <div className="px-4 pt-3 pb-4 overflow-y-auto flex-grow">
            <div
              className="space-y-0 rounded-xl overflow-hidden"
              style={{ backgroundColor: listAreaBgColor }}
            >
              {modes.map((mode) => {
                const isSelected = contentMode === mode.id;
                return (
                  <button
                    key={mode.id}
                    onClick={() => {
                      onChange(mode.id);
                      setIsOpen(false);
                    }}
                    className={`w-full text-left p-4 transition-colors border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)] last:border-b-0 ${
                      isSelected ? 'bg-[var(--text-color)]/[0.08]' : 'hover:bg-[var(--text-color)]/5'
                    }`}
                  >
                    <div className="font-medium text-sm" style={{ color: 'var(--text-color)' }}>{mode.title}</div>
                    <div className="text-xs opacity-70 mt-1" style={{ color: 'var(--text-color)' }}>{mode.description}</div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="relative" ref={sheetRef}>
      {renderContentModeButton()}
      {renderContentModeSheet()}
    </div>
  );
};

export default ContentModeSelector; 