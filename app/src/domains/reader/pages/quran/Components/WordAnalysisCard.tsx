import React from 'react';
import type { MergedWord } from '@reader/hooks/useWordAnalysis';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface WordAnalysisCardProps {
  wordData: MergedWord;
}

// Gösterilecek analiz alanlarını ve sıralarını belirliyoruz
const analysisKeys: (keyof MergedWord['analysis'])[] = [
  'kok',
  'turkce_gramer',
  'arapca_gramer',
  'zaman',
  'sarf_bilgisi',
  'bab',
];

export const WordAnalysisCard: React.FC<WordAnalysisCardProps> = ({ wordData }) => {
  const cardBgColor = useAutoOverlay(5, 'var(--bg-color)');
  const borderColor = useAutoOverlay(10, 'var(--bg-color)');

  return (
    <div 
      className="flex-shrink-0 w-auto min-w-[250px] rounded-lg p-4 flex flex-col shadow-sm"
      style={{ 
        backgroundColor: cardBgColor,
        borderColor: borderColor,
        borderWidth: '1px',
        borderStyle: 'solid'
      }}
    >
      
      {/* Üst Kısım: Kelime Bilgileri */}
      <div 
        className="text-center mb-3 pb-3 border-b"
        style={{ borderColor: borderColor }}
      >
        <p 
          className="text-4xl font-quranic rtl-text mb-2"
          style={{ color: 'var(--text-color)' }}
        >
          {wordData.arabic}
        </p>
        <p 
          className="text-base opacity-80"
          style={{ color: 'var(--text-color)' }}
        >
          {wordData.meaning}
        </p>
      </div>

      {/* Alt Kısım: Morfolojik Detaylar (Sadece Değerler) */}
      <div className="space-y-2 flex-grow pt-2">
        {analysisKeys.map(key => {
          const value = wordData.analysis[key];
          return (
            <div key={key} className="text-center">
              <span 
                className="text-sm opacity-70"
                style={{ color: 'var(--text-color)' }}
              >
                {value || '-'}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};
