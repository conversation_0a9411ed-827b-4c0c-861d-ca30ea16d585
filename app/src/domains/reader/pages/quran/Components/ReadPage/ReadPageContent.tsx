import React from 'react';
import { NavigationFooter } from '@shared/components';
import { SurahHeader } from '../SurahHeader';
import { VerseList } from '../VerseList';
import { Surah, CombinedVerseData, ITranslator } from '@reader/models/types';
import { ContentMode } from '@reader/hooks/quran/useContentMode';

interface ReadPageContentProps {
  // Data
  displayedSurahInfo: Surah;
  displayVerses: CombinedVerseData[];
  surahs: Surah[] | null;
  
  // UI State
  contentMode: ContentMode;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  translationsLoading: boolean;
  
  // Colors
  translatorTitleColor: string;
  footnoteColor: string;
  sectionLeftBorderColor: string;
  verseBorderColor: string;
  
  // Handlers
  onVerseClick: (verseNo: number) => void;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  handleNavigation: (surahId: number) => void;
}

export const ReadPageContent: React.FC<ReadPageContentProps> = ({
  displayedSurahInfo,
  displayVerses,
  surahs,
  contentMode,
  selectedTranslators,
  availableTranslators,
  translationsLoading,
  translatorTitleColor,
  footnoteColor,
  sectionLeftBorderColor,
  verseBorderColor,
  onVerseClick,
  onVerseMenuClick,
  handleNavigation,
}) => {
  return (
    <div className="pb-20">
      <main className="max-w-3xl mx-auto px-5 py-10 space-y-10">
        <SurahHeader currentSurahInfo={displayedSurahInfo} />

        <VerseList
          verses={displayVerses}
          viewMode={contentMode}
          selectedTranslators={selectedTranslators}
          availableTranslators={availableTranslators}
          translatorTitleColor={translatorTitleColor}
          footnoteColor={footnoteColor}
          sectionLeftBorderColor={sectionLeftBorderColor}
          verseBorderColor={verseBorderColor}
          onVerseClick={onVerseClick}
          onVerseMenuClick={onVerseMenuClick}
          surahName={displayedSurahInfo?.name}
        />

        <NavigationFooter
          prevSection={displayedSurahInfo && displayedSurahInfo.id > 1 ? {
            id: String(displayedSurahInfo.id - 1),
            title: surahs?.find((s: Surah) => s.id === displayedSurahInfo.id - 1)?.name || '',
            onClick: () => handleNavigation(displayedSurahInfo.id - 1)
          } : undefined}
          nextSection={displayedSurahInfo && displayedSurahInfo.id < 114 ? {
            id: String(displayedSurahInfo.id + 1),
            title: surahs?.find((s: Surah) => s.id === displayedSurahInfo.id + 1)?.name || '',
            onClick: () => handleNavigation(displayedSurahInfo.id + 1)
          } : undefined}
        />

        {translationsLoading && (
          <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50">
            <div className="bg-[var(--bg-color)] px-4 py-2 rounded-full shadow-lg flex items-center space-x-2"
                 style={{ color: 'var(--text-color)', border: '1px solid rgba(var(--text-color-rgb), 0.1)' }}>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Mealler yükleniyor...</span>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};
