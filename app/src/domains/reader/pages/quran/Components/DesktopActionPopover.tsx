import React, { useState, useRef, useEffect } from 'react';
import { 
  useFloating, 
  FloatingPortal, 
  useClick, 
  useDismiss, 
  useRole, // Erişilebilirlik için rol ataması
  useFocus, // Odak yönetimi
  useListNavigation, // Liste navigasyonu (ok tuşları)
  useInteractions, 
  flip, 
  shift, 
  offset 
} from '@floating-ui/react';
import type { Placement } from '@floating-ui/react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

// Menü elemanı için daha gü<PERSON>li bir tip tanımı yapıyoruz
export interface ActionMenuItem {
  icon: React.ElementType;
  text: string;
  action: string;
  handler?: (e: React.MouseEvent) => void;
  isGreen?: boolean;
}

interface DesktopActionPopoverProps {
  isOpen?: boolean;
  onClose?: () => void;
  sheetTitle: string;
  menuItems: ActionMenuItem[]; // Artık any[] yerine ActionMenuItem[] kullanıyo<PERSON>z
  executeAction: (e: React.MouseEvent, action: ((e: React.MouseEvent) => void) | undefined) => void;
  anchorEl: HTMLElement | null;
  placement?: Placement;
}

export const DesktopActionPopover: React.FC<DesktopActionPopoverProps> = ({
  isOpen,
  onClose,
  sheetTitle,
  menuItems,
  executeAction,
  anchorEl,
  placement = 'left-start',
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const listRef = useRef<Array<HTMLButtonElement | null>>([]);

  // Hook always called at the component level
  const popoverBgColor = useAutoOverlay(12, 'var(--bg-color)');

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => !open && onClose?.(),
    placement: placement,
    elements: {
      reference: anchorEl,
    },
    middleware: [
      offset(10),
      flip(),
      shift(),
    ],
  });

  // --- Erişilebilirlik için Etkileşim Kurulumu --- //
  const focus = useFocus(context);
  const click = useClick(context);
  const dismiss = useDismiss(context);
  const role = useRole(context, { role: 'menu' });
  const listNavigation = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
  });

  const { getFloatingProps, getItemProps } = useInteractions([
    focus,
    click,
    dismiss,
    role,
    listNavigation,
  ]);

  // Menü kapandığında aktif indeksi sıfırla
  useEffect(() => {
    if (!isOpen) {
      setActiveIndex(null);
    }
  }, [isOpen]);

  if (!isOpen || !anchorEl) return null;

  return (
    <FloatingPortal>
      <div
        ref={refs.setFloating}
        style={floatingStyles}
        {...getFloatingProps()}
        className="z-50"
      >
        <div 
          className="w-52 rounded-xl shadow-2xl border border-opacity-20 overflow-hidden"
          style={{ backgroundColor: popoverBgColor, borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)' }}
        >
          <div className="px-3 py-2 border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)]">
            <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>{sheetTitle}</span>
          </div>
          <div className="py-1" role="menu">
            {menuItems.map((item, index) => (
              <button 
                key={item.action} 
                ref={(node) => { listRef.current[index] = node; }} // Klavye navigasyonu için referans
                role="menuitem"
                tabIndex={activeIndex === index ? 0 : -1} // Sadece aktif eleman focus edilebilir olsun
                {...getItemProps({ // Etkileşim propları
                  onClick: (e) => executeAction(e, item.handler),
                })}
                disabled={!item.handler} 
                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-[var(--text-color)]/5 focus:bg-[var(--text-color)]/10 outline-none"
                style={{ color: item.isGreen ? 'var(--success-color)' : 'var(--text-color)' }}
              >
                <item.icon size={16} className="mr-3 opacity-80 flex-shrink-0" />
                <span className="flex-grow truncate">{item.text}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </FloatingPortal>
  );
};
