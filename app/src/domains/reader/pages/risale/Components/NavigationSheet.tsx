// import React, { useState, useEffect } from 'react'; // Removed unused React import
import { X, ChevronRight, Loader2 } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { autoHue } from '@shared/hooks/autohue';
import { RisaleSectionDef } from '@reader/models/types';

// Manual book list with correct IDs from books.ts - Only category_id = 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>ur Külliyatı)
const risaleBooks = [
  { id: 5, title: "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", author: "<PERSON> Nursi" },
  { id: 6, title: "<PERSON><PERSON>", author: "<PERSON> Nursi" },
  { id: 7, title: "Emirdağ Lahikası - 1", author: "<PERSON> Nursi" },
  { id: 8, title: "<PERSON><PERSON><PERSON><PERSON> 2", author: "<PERSON> Nursi" },
  { id: 9, title: "<PERSON>şara<PERSON><PERSON><PERSON>z", author: "<PERSON>" },
  { id: 10, title: "<PERSON><PERSON><PERSON><PERSON>", author: "<PERSON>" },
  { id: 11, title: "<PERSON><PERSON><PERSON><PERSON><PERSON>", author: "<PERSON> Nursi" },
  { id: 12, title: "<PERSON><PERSON><PERSON><PERSON>", author: "<PERSON> Nursi" },
  { id: 13, title: "<PERSON>s<PERSON>vi-i <PERSON><PERSON><PERSON>", author: "<PERSON> <PERSON><PERSON>i" },
  { id: 14, title: "<PERSON><PERSON><PERSON>t", author: "<PERSON> <PERSON><PERSON>i" },
  { id: 15, title: "<PERSON><PERSON><PERSON>-i <PERSON>sdi<PERSON>-i <PERSON>bi", author: "Said <PERSON><PERSON>i" },
  { id: 16, title: "S<PERSON>zler", author: "Said <PERSON><PERSON>i" },
  { id: 17, title: "<PERSON>ualar", author: "Said <PERSON><PERSON>i" },
  { id: 18, title: "Tarihçe-i Hayat", author: "Said Nursi" }
];

interface NavigationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  navigationType: 'section' | 'books';
  onChangeNavigationType: (type: 'section' | 'books') => void;
  bookId: string;
  bookTitle?: string;
  sectionSearch: string;
  onChangeSectionSearch: (search: string) => void;
  bookSearch: string;
  onChangeBookSearch: (search: string) => void;
  currentSectionId?: string;
  filteredSections: RisaleSectionDef[];
  onSelectSection: (sectionId: string) => void;
  onSelectBook: (bookId: number) => void;
  onSwitchBookInSheet: (bookId: number) => void;
  isLoadingSections: boolean;
}

const NavigationSheet = ({
  isOpen,
  onClose,
  navigationType,
  onChangeNavigationType,
  bookId,
  bookTitle,
  sectionSearch,
  onChangeSectionSearch,
  bookSearch = '',
  onChangeBookSearch = () => {},
  currentSectionId,
  filteredSections,
  onSelectSection,
  onSelectBook = () => {},
  onSwitchBookInSheet = () => {},
  isLoadingSections = false,
}: NavigationSheetProps) => {
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  if (!isOpen) return null;
  
  const activeStyle = autoHue('var(--text-color)');
  const defaultStyle = { color: 'var(--text-color)' };

  // Filter the books based on search
  const filteredBooks = bookSearch.trim() 
    ? risaleBooks.filter(book => 
        book.title.toLowerCase().includes(bookSearch.toLowerCase()) || 
        book.author.toLowerCase().includes(bookSearch.toLowerCase())
      )
    : risaleBooks;
  
  const selectBookAndShowSections = (bookId: number) => {
    onSwitchBookInSheet(bookId);
  };
  
  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />
      
      {/* Sheet - Similar styling to Quran version */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />
        
        {/* Header with Book Title */}
        <div className="mx-4 mt-3 mb-2">
          <h3 className="text-base font-medium text-[var(--text-color)]">
            {bookTitle || 'Risale-i Nur'}
          </h3>
        </div>

        {/* Segment Control with Close Button */}
        <div className="mx-4 mt-2 flex items-center justify-between">
          <div className="flex-1 flex items-center p-1 rounded-xl overflow-hidden" style={{
            backgroundColor: toolbarBgColor
          }}>
            <button
              onClick={() => onChangeNavigationType('books')}
              className={`flex-1 px-2 py-2 rounded-xl text-sm font-medium transition-colors ${
                navigationType === 'books' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={navigationType === 'books' ? activeStyle : defaultStyle}
            >
              Külliyat
            </button>
            
            <span className="text-[var(--text-color)] opacity-40 mx-1">|</span>
            
            <button
              onClick={() => onChangeNavigationType('section')}
              className={`flex-1 px-2 py-2 rounded-xl text-sm font-medium transition-colors ${
                navigationType === 'section' ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
              }`}
              style={navigationType === 'section' ? activeStyle : defaultStyle}
            >
              Bölümler
            </button>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center ml-2 rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content - Navigation sections */}
        <div className="px-4 pt-3 pb-6 overflow-y-auto max-h-[calc(80vh-10rem)] md:max-h-[calc(70vh-10rem)] mt-2">
          {/* Books Tab */}
          {navigationType === 'books' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={bookSearch}
                  onChange={(e) => onChangeBookSearch(e.target.value)}
                  placeholder="Kitap adı ara..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70`}
                  style={{ 
                    backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                  }}
                />
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </div>
              <div 
                className="space-y-0 p-3 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                <div className="mb-2">
                  <h4 className="text-xs font-medium uppercase opacity-50 px-2 py-1 text-[var(--text-color)]">
                    Risale-i Nur Külliyatı
                  </h4>
                </div>
                
                {filteredBooks.map((book) => {
                  const isCurrentBook = book.id.toString() === bookId;
                  const bookNameStyle = isCurrentBook 
                    ? activeStyle
                    : defaultStyle;
                    
                  return (
                    <button
                      key={book.id}
                      onClick={() => {
                        onSelectBook(book.id);
                      }}
                      className={`w-full px-4 py-3 text-left rounded-none text-sm flex items-start justify-between relative hover:bg-[var(--text-color)]/5 text-[var(--text-color)] border-b border-[color-mix(in_srgb,var(--text-color)_15%,transparent)] last:border-b-0 ${
                        isCurrentBook ? 'bg-[var(--text-color)]/[0.08]' : ''
                      }`}
                      style={defaultStyle}
                    >
                      <div className="flex-1 flex-col">
                        <span 
                          className="break-words font-medium max-w-full" 
                          style={bookNameStyle}
                        >
                          {book.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <button 
                          className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            selectBookAndShowSections(book.id);
                          }}
                          title={`${book.title} Bölümleri`}
                        >
                          <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />
                        </button>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Sections Tab */}
          {navigationType === 'section' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={sectionSearch}
                  onChange={(e) => onChangeSectionSearch(e.target.value)}
                  placeholder="Bölüm adı veya numarası ara..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70`}
                  style={{ 
                    backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                  }}
                />
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </div>
              <div 
                className="space-y-0 p-3 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {isLoadingSections ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 size={24} className="animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  filteredSections.map(section => {
                    const isCurrent = section.id === currentSectionId;
                    const style = isCurrent ? activeStyle : defaultStyle;

                    return (
                      <button
                        key={section.id}
                        onClick={() => onSelectSection(section.id)}
                        className={`w-full px-4 py-3 text-left rounded-none text-sm font-medium relative hover:bg-[var(--text-color)]/5 text-[var(--text-color)] border-b border-[color-mix(in_srgb,var(--text-color)_15%,transparent)] last:border-b-0 ${
                          isCurrent ? 'bg-[var(--text-color)]/[0.08]' : ''
                        }`}
                        style={style}
                      >
                        {section.title}
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NavigationSheet; 