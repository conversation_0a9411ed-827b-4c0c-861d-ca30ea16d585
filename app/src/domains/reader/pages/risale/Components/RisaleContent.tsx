import React, { useMemo, forwardRef } from 'react';
import { RisaleSentence } from '@reader/models/types';
import { TextSelectionHandler } from '@domains/reader-interactions/text-selection';
import { TextHighlighter, TextHighlighterStyles } from '@domains/reader-interactions/highlights/components/TextHighlighter';
import { AnnotationFloatingPanel } from '@domains/reader-interactions/shared';
import { useAnnotationManager } from '@domains/reader-interactions/annotations';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useParams } from 'react-router-dom';

interface RisaleContentProps {
  sentences: RisaleSentence[] | null;
}

const RisaleContent = forwardRef<HTMLDivElement, RisaleContentProps>((
  { sentences }, 
  ref
) => {
  // This log verifies the final data that the component attempts to render.
  React.useEffect(() => {
    if(sentences) {
      console.log('[DEBUG] RisaleContent: Received sentences prop for rendering:', JSON.parse(JSON.stringify(sentences)));
    }
  }, [sentences]);

  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const user = useAuthStore((state) => state.user);
  
  const { annotations, loadAnnotations } = useAnnotationManager({
    book_id: bookId,
    section_id: sectionId,
    user_id: user?.id,
  });

  const getAlignmentClass = (alignment?: 'left' | 'center' | 'right'): string => {
    switch (alignment) {
      case 'center': return 'text-center';
      case 'right': return 'text-right';
      default: return 'text-left';
    }
  };

  const allSentences = useMemo(() => sentences || [], [sentences]);

  const renderContent = () => {
    if (!sentences) return null;

    const paragraphs: JSX.Element[] = [];
    let currentParagraphSentences: RisaleSentence[] = [];

    const processParagraph = () => {
      if (currentParagraphSentences.length === 0) return;

      const alignment = getAlignmentClass(currentParagraphSentences[0]?.alignment);

      const paragraphContent = currentParagraphSentences.map((sentence) => {
        const sentenceAnnotations = annotations.filter(annotation =>
          Array.isArray(annotation.sentence_id)
            ? annotation.sentence_id.includes(sentence.id!)
            : annotation.sentence_id === sentence.id
        ).map(annotation => ({
          ...annotation,
          annotation_type: annotation.annotation_type === 'sherh' ? 'note' : annotation.annotation_type
        } as import('../../../../reader-interactions/highlights/types/highlight.types').Annotation));

        const textElement = (
          <TextHighlighter
            text={sentence.text || ''}
            annotations={sentenceAnnotations}
            onAnnotationClick={() => {}}
            sentenceId={sentence.id || ''}
            allSentences={allSentences.filter(s => s.id && s.text).map(s => ({ id: s.id!, text: s.text! }))}
          />
        );

        const verseBreak = sentence.verse_break_after ? (
          <span
            className="verse-separator inline-block w-3 h-3 rounded-full bg-current opacity-40 mx-2 align-middle"
            title="Ayet Sonu"
          />
        ) : ' ';

        // Eğer line_break varsa, bu cümleyi kendi div'i içinde render et (blok element).
        if (sentence.line_break) {
          return (
            <div key={sentence.id} id={`sentence-${sentence.id}`} data-sentence-id={sentence.id}>
              {textElement}
              {verseBreak}
            </div>
          );
        }

        // Yoksa, satır içi devam etmesi için span kullan.
        return (
          <span key={sentence.id} id={`sentence-${sentence.id}`} data-sentence-id={sentence.id}>
            {textElement}
            {verseBreak}
          </span>
        );
      });

      paragraphs.push(
        <div key={`p-${paragraphs.length}`} className={`mb-4 ${alignment}`}>
          {paragraphContent}
        </div>
      );

      const lastSentence = currentParagraphSentences[currentParagraphSentences.length - 1];
      if (lastSentence.divider_after) {
        paragraphs.push(<hr key={`d-${paragraphs.length}`} className="my-6 w-4/5 mx-auto border-t-2" style={{ borderColor: 'color-mix(in srgb, var(--text-color) 30%, transparent)' }} />);
      }

      currentParagraphSentences = [];
    };

    sentences.forEach(sentence => {
      if (sentence.paragraph_start && currentParagraphSentences.length > 0) {
        processParagraph();
      }
      currentParagraphSentences.push(sentence);
    });

    processParagraph();

    return paragraphs;
  };

  return (
    <>
      <TextHighlighterStyles />
      <div ref={ref}>
        <TextSelectionHandler
          className="risale-content leading-relaxed text-lg"
          onAnnotationCreated={loadAnnotations}
        >
          <div lang="tr">
            {renderContent()}
          </div>
        </TextSelectionHandler>
      </div>
      <AnnotationFloatingPanel
        showNotes={true}
        setShowNotes={() => {}}
        showHighlights={true}
        setShowHighlights={() => {}}
        showBookmarks={true}
        setShowBookmarks={() => {}}
      />
    </>
  );
});

export default RisaleContent;