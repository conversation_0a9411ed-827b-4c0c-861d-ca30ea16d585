import React, { useRef, useState, useEffect } from 'react';
import parser, { HTMLReactParserOptions } from 'html-react-parser';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { TextActionMenu } from '@domains/reader-interactions/text-selection';
// ❌ DEPRECATED: AnnotationForm artık AnnotationSheet olarak text-selection domain'inde
// import { AnnotationForm } from '@domains/reader-interactions/components/AnnotationForm';
import { AnnotationType } from '@domains/reader-interactions/shared/types';

interface RisaleParagraphProps {
  paragraph: string;
  index: number;
  parserOptions: HTMLReactParserOptions;
  processRisaleParagraph: (paragraph: string) => string;
  bookId: string;   // Kitap ID'si (Risale-i Nur için)
  sectionId: string; // Bölüm ID'si
}

const RisaleParagraph: React.FC<RisaleParagraphProps> = ({
  paragraph,
  index,
  parserOptions,
  processRisaleParagraph
}) => {
  const paragraphRef = useRef<HTMLParagraphElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  
  const [selectedText, setSelectedText] = useState<string>('');

  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);

  const [formVisible, setFormVisible] = useState(false);
  
  // Boş paragrafları sadece boşluk olarak render et
  if (!paragraph.trim()) {
    return <div key={`p-${index}`} className="h-4"></div>;
  }

  // Dışarı tıklama ile UI'ları kapat
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // Paragrafı işle
  const processedParagraph = processRisaleParagraph(paragraph);
  
  // Seçim işleyicisi
  const handleMouseUp = () => {
    const currentSelection = window.getSelection();
    
    if (currentSelection && !currentSelection.isCollapsed &&
        paragraphRef.current?.contains(currentSelection.anchorNode)) {
      
      setSelectedText(currentSelection.toString());
      
      // Menü pozisyonunu hesapla
      const range = currentSelection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      setMenuPosition({
        top: rect.top,
        left: rect.left + (rect.width / 2)
      });
      
      setMenuVisible(true);
    }
  };
  
  // Action seçildiğinde
  const handleActionSelect = (actionType: AnnotationType | 'copy' | 'share' | 'comment') => {
    // Kopyalama, paylaşma gibi işlemler için menu kapatıp form açma
    if (actionType === 'copy' || actionType === 'share' || actionType === 'comment') {
      if (actionType === 'copy') {
        // Kopyalama işlemi
        navigator.clipboard.writeText(selectedText);
      }
      
      setMenuVisible(false);
      return;
    }
    
    // Annotation tipindeki aksiyonlar için form aç
    setMenuVisible(false);
    setFormVisible(true);
  };
  
  // Dışarı tıklama ile UI'ları kapat
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuVisible(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [menuRef]);
  
  return (
    <div className="relative mb-3">
      <p 
        ref={paragraphRef}
        className="relative annotation-paragraph"
        onMouseUp={handleMouseUp}
        data-paragraph-index={index}
      >
        {parser(processedParagraph, parserOptions)}
      </p>
      
      {/* Metin seçme menüsü */}
      <TextActionMenu 
        isVisible={menuVisible}
        position={menuPosition}
        menuRef={menuRef}
        selectedText={selectedText}
        onActionSelect={handleActionSelect}
      />
      
      {/* ❌ DEPRECATED: AnnotationForm kaldırıldı, artık TextSelectionHandler kullanılıyor */}
      {/* Yeni sistem için RisaleContent.tsx'deki TextSelectionHandler kullanın */}
      {formVisible && (
        <div className="text-sm text-gray-500 p-2 border rounded">
          ⚠️ Bu component deprecated. Yeni TextSelectionHandler kullanın.
        </div>
      )}
    </div>
  );
};

export default RisaleParagraph; 