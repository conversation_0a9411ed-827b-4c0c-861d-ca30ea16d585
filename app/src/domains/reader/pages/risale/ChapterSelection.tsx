import { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  PageLayout,
  SearchBar,
  ContentCard,
  LoadingState,
  EmptyState
} from '../../../../shared/components';
import { useRisaleBook } from '@reader/hooks/risale/useRisaleBook';
import { RisaleSectionDef } from '@reader/models/types';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { usePageLayout, useErrorHandling } from '@reader/hooks';
import { CONTAINER_SIZES, SPACING, GRID_LAYOUTS } from '@reader/constants/layout';

const ChapterSelectionPage = () => {
  const { bookId } = useParams<{ bookId: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const isMobile = useIsMobile();
  const { renderErrorState } = useErrorHandling();

  // Veri yükleme için hook'u kullan
  const { data: bookData, loading, error } = useRisaleBook(bookId);

  // Page layout config - always call hooks at the top
  const pageTitle = bookId ? `Risale (${bookId})` : 'Risale';
  const pageLayoutConfig = usePageLayout({
    title: pageTitle,
    isMobile
  });

  // Arama filtresi (hook'tan gelen veriye göre)
  const filteredSections = useMemo(() =>
    bookData?.structure?.sections.filter((section: RisaleSectionDef) =>
      section.title.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [],
    [bookData?.structure?.sections, searchTerm]
  );

  // Yükleniyor durumu
  if (loading) {
    return <LoadingState message="Kitap bilgileri yükleniyor..." />;
  }

  // Hata durumu
  if (error) {
    return (
      <PageLayout {...pageLayoutConfig}>
        {renderErrorState({ error }, 'Risale bölümleri')}
      </PageLayout>
    );
  }

  // Veri yok veya boş durumu
  if (!bookData || !bookData.structure || bookData.structure.sections.length === 0) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <EmptyState message="Bu kitap için bölüm bilgisi bulunamadı." />
      </PageLayout>
    );
  }

  // Başarılı durum: Veri yüklendi
  const currentPageTitle = bookData.metadata.title || pageTitle;
  const currentPageLayoutConfig = {
    ...pageLayoutConfig,
    title: currentPageTitle
  };

  return (
    <PageLayout {...currentPageLayoutConfig}>
      {/* SearchBar */}
      <div className={`mt-4 mb-4 ${CONTAINER_SIZES.wide} mx-auto ${SPACING.containerMobile}`}>
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Bölüm ara..."
        />
      </div>

      {/* Bölüm Kartları */}
      <div className={`${CONTAINER_SIZES.wide} mx-auto ${SPACING.containerMobile}`}>
        <div className={`grid ${GRID_LAYOUTS.sections} ${SPACING.card}`}>
          {filteredSections.map((section: RisaleSectionDef) => (
            <div key={section.id} className="flex">
              <ContentCard
                title={section.title}
                onClick={() => navigate(`/risale/${bookId}/${section.id}`)}
                style={{
                  minHeight: '70px',
                  width: '100%'
                }}
              />
            </div>
          ))}
        </div>

        {/* Arama sonucu yok mesajı */}
        {filteredSections.length === 0 && searchTerm && (
          <EmptyState message="Arama sonucu bulunamadı" />
        )}
      </div>
    </PageLayout>
  );
};

export default ChapterSelectionPage;